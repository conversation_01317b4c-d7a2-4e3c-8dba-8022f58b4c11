#!/usr/bin/env python3
"""
Migration script to fix UUID type issues in PostgreSQL database.

This script:
1. Checks for string UUIDs in UUID columns
2. Converts them to proper UUID type
3. Validates the data integrity

Run this script after updating the codebase to handle UUID types properly.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from api.config import settings
from api.utils.logger import logger
import uuid


def validate_uuid(uuid_string):
    """Validate if a string is a valid UUID"""
    try:
        uuid.UUID(str(uuid_string))
        return True
    except (ValueError, AttributeError):
        return False


def fix_uuid_columns():
    """Fix UUID columns that might have string values"""
    
    # Only run for PostgreSQL
    if "postgresql" not in settings.database_url:
        logger.info("Database is not PostgreSQL, skipping migration")
        return
    
    engine = create_engine(settings.database_url)
    Session = sessionmaker(bind=engine)
    
    with Session() as session:
        try:
            # Start transaction
            session.begin()
            
            # Check if we need to fix anything
            logger.info("Checking for UUID type issues...")
            
            # Get all tables with UUID columns
            tables_to_check = [
                ('upload_sessions', 'id'),
                ('uploaded_files', 'id'),
                ('uploaded_files', 'session_id'),
                ('file_processing_results', 'id'),
                ('file_processing_results', 'file_id')
            ]
            
            issues_found = False
            
            for table_name, column_name in tables_to_check:
                # Check column type
                result = session.execute(text(f"""
                    SELECT data_type 
                    FROM information_schema.columns 
                    WHERE table_name = :table_name 
                    AND column_name = :column_name
                """), {"table_name": table_name, "column_name": column_name})
                
                row = result.first()
                if row and row[0] != 'uuid':
                    logger.warning(f"{table_name}.{column_name} is not UUID type: {row[0]}")
                    issues_found = True
                
                # Check for invalid UUIDs (this would fail if column is already UUID type)
                try:
                    result = session.execute(text(f"""
                        SELECT COUNT(*) 
                        FROM {table_name} 
                        WHERE {column_name} IS NOT NULL 
                        AND {column_name}::text !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
                    """))
                    
                    count = result.scalar()
                    if count > 0:
                        logger.warning(f"Found {count} invalid UUIDs in {table_name}.{column_name}")
                        issues_found = True
                except Exception as e:
                    # Column is already UUID type, which is good
                    logger.debug(f"{table_name}.{column_name} is already UUID type")
            
            if not issues_found:
                logger.info("No UUID type issues found")
                return
            
            # Fix issues by creating proper UUID columns
            logger.info("Fixing UUID type issues...")
            
            # For each table, we need to:
            # 1. Add temporary UUID columns
            # 2. Copy and convert data
            # 3. Drop old columns
            # 4. Rename new columns
            
            # This is a complex migration that should be done carefully
            # For now, we'll just log what needs to be done
            
            logger.warning("Manual migration required!")
            logger.warning("Please run the following SQL commands:")
            
            for table_name, column_name in tables_to_check:
                if column_name.endswith('_id'):
                    # Foreign key column
                    logger.warning(f"""
-- Fix {table_name}.{column_name}
ALTER TABLE {table_name} ADD COLUMN {column_name}_new UUID;
UPDATE {table_name} SET {column_name}_new = {column_name}::uuid WHERE {column_name} IS NOT NULL;
ALTER TABLE {table_name} DROP CONSTRAINT IF EXISTS {table_name}_{column_name}_fkey;
ALTER TABLE {table_name} DROP COLUMN {column_name};
ALTER TABLE {table_name} RENAME COLUMN {column_name}_new TO {column_name};
                    """)
                else:
                    # Primary key column
                    logger.warning(f"""
-- Fix {table_name}.{column_name}
ALTER TABLE {table_name} ADD COLUMN {column_name}_new UUID DEFAULT gen_random_uuid();
UPDATE {table_name} SET {column_name}_new = {column_name}::uuid WHERE {column_name} IS NOT NULL;
ALTER TABLE {table_name} DROP CONSTRAINT {table_name}_pkey;
ALTER TABLE {table_name} DROP COLUMN {column_name};
ALTER TABLE {table_name} RENAME COLUMN {column_name}_new TO {column_name};
ALTER TABLE {table_name} ADD PRIMARY KEY ({column_name});
                    """)
            
            session.commit()
            
        except Exception as e:
            logger.error(f"Error during migration: {str(e)}", exc_info=True)
            session.rollback()
            raise


if __name__ == "__main__":
    fix_uuid_columns()
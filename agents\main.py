import logging
import os
from typing import List, Dict, Any, Union, Optional, Callable
from pydantic import BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel

# Import our custom tools
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_agent.base import search_qdrant, generate_answer
from api_agent.base import search_sec_filings

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Global callback for real-time updates
_status_callback: Optional[Callable[[str, str], None]] = None

# Global storage for tool responses to preserve citation data
_tool_responses: List[Dict[str, Any]] = []

def set_status_callback(callback: Optional[Callable[[str, str], None]]):
    """Set the global status callback function."""
    global _status_callback
    _status_callback = callback

def emit_status(agent: str, message: str):
    """Emit a status update if callback is set."""
    if _status_callback:
        _status_callback(agent, message)
    logger.info(f"[{agent}] {message}")

def clear_tool_responses():
    """Clear the global tool responses storage."""
    global _tool_responses
    _tool_responses = []

def store_tool_response(response: Dict[str, Any]):
    """Store a tool response for later retrieval."""
    global _tool_responses
    _tool_responses.append(response)

class QueryContext(BaseModel):
    """Context for the agent query."""
    user_id: str = "default_user"
    collection_name: str = "documents"
    qdrant_url: str = "http://localhost:6333"


class FinancialDataResponse(BaseModel):
    """Response model for financial data queries."""
    source: str  # "qdrant", "sec_api", "mixed", or "error"
    answer: str  # Generated answer to the user's question
    query: str  # Original query
    summary: str  # Summary of what was found and methodology used
    
    # RAG-specific fields (populated when RAG is used)
    confidence_score: float = 0.0  # Confidence in the answer (0-1)
    used_context_indices: List[int] = []  # Indices of context snippets used
    used_doc_ids: List[str] = []  # Document IDs of used snippets
    rag_citations: List[Dict[str, Any]] = []  # Detailed RAG citations
    
    # SEC-specific fields (populated when SEC API is used)
    sec_sources: List[Dict[str, Any]] = []  # SEC filing sources used
    sec_citations: List[Dict[str, Any]] = []  # Detailed SEC citations
    
    # Raw data for transparency
    data: List[Dict[str, Any]] = []  # Raw search results for reference


# Initialize the Pydantic AI agent
financial_agent = Agent(
    model=OpenAIModel('gpt-4o-mini'),
    result_type=FinancialDataResponse,
    retries=3,
    result_retries=2,
    system_prompt="""
You are a financial data orchestrator and analyst that provides comprehensive answers to financial questions.

You have access to two main data sources:
1. RAG Database (Qdrant): Contains processed financial documents like earnings reports, 
   audited financial statements, revenue bridges, cap tables, and other financial analysis.
2. SEC Filing API: Provides access to raw SEC filings (10-K, 10-Q, 8-K, etc.) from public companies.

Your primary goal is to ANSWER the user's question comprehensively, not just find information.

DECISION LOGIC:
1. For queries about processed financial analysis (earnings analysis, revenue trends, quality of earnings, 
   working capital analysis, forecasts), start with RAG Database
2. For queries about SEC filings, regulatory submissions, or recent company disclosures, use SEC Filing API
3. For comprehensive analysis questions, use BOTH sources to provide a complete answer

RESPONSE GENERATION RULES:
- Always provide a direct, comprehensive answer to the user's question
- Combine insights from both sources when relevant
- ALWAYS extract and include citation data from tool responses
- Synthesize information rather than just listing search results
- A comprehensive answer must include not only factual retrieval but also:
  - Analytical reasoning
  - Reconciliation of key differences (e.g., GAAP vs non-GAAP figures)
  - Brief commentary on implications or materiality
  - Cross-source confirmation when data aligns (e.g., SEC-reported vs adjusted figures)
  - Computation and explanation of key financial deltas (e.g., EPS variance, revenue change)
- Responses should reflect the clarity, precision, and conciseness expected from an investment analyst preparing a board-level summary or due diligence memo
- Avoid vague conclusions (e.g., "strong performance") unless backed by concrete data or citations
- For comparison questions (e.g., reported vs adjusted EPS), always explain the source and nature of the difference

CRITICAL: When your tools return responses, you MUST extract the following data from tool responses:

From RAG tool responses:
- confidence_score (if available)
- used_context_indices (if available)  
- used_doc_ids (if available)
- rag_citations (if available) - ALWAYS include this in your response

From SEC tool responses:
- sec_sources (if available)
- sec_citations (if available) - ALWAYS include this in your response

RESPONSE FORMAT REQUIREMENTS:
You MUST return a response with exactly these fields:

- source: String - "qdrant" (RAG only), "sec_api" (SEC only), "mixed" (both sources), or "error"
- answer: String - Your comprehensive answer to the user's question (REQUIRED - never empty)
- query: String - The exact original query provided by the user
- summary: String - Brief summary of methodology and data sources used
- data: List - Raw data from tool responses for transparency

MANDATORY CITATION FIELDS (extract from tool responses):
- confidence_score: Float - Extract from RAG tool response
- used_context_indices: List[int] - Extract from RAG tool response
- used_doc_ids: List[str] - Extract from RAG tool response  
- rag_citations: List[Dict] - Extract from RAG tool response (CRITICAL)
- sec_sources: List[Dict] - Extract from SEC tool response
- sec_citations: List[Dict] - Extract from SEC tool response (CRITICAL)

IMPORTANT GUIDELINES:
- NEVER return empty answers - always provide substantive responses
- ALWAYS preserve citation data from tool responses - this is critical for source attribution
- When combining sources, synthesize the information cohesively
- Acknowledge limitations when data is insufficient
- Provide actionable insights when possible
- Include specific numbers, dates, and metrics when available

Example workflow:
1. Call relevant tools to get data
2. Extract citation information from tool responses
3. Generate comprehensive answer
4. Include ALL citation data in final response
    """,
)


@financial_agent.tool
async def search_financial_documents(
    ctx: RunContext[QueryContext], 
    query: str,
) -> Dict[str, Any]:
    """
    Search financial documents in the RAG database and generate a comprehensive answer.
    
    Use this tool for queries about:
    - Earnings reports and analysis
    - Revenue bridge analysis  
    - Cap table information
    - Quality of earnings reports
    - Audited financial statements
    - Financial metrics and KPIs
    """
    try:
        emit_status("RAG_AGENT", "🚀 RAG Agent invoked - Searching financial documents database...")
        
        emit_status("RAG_AGENT", "🔍 Performing hybrid vector search (dense + sparse embeddings)...")
        results = search_qdrant(
            query=query,
            user_id=ctx.deps.user_id,
            collection_name=ctx.deps.collection_name,
            qdrant_url=ctx.deps.qdrant_url,
            k=5
        )
        
        # Ensure results is a list
        if not isinstance(results, list):
            results = []
        
        emit_status("RAG_AGENT", f"📄 Retrieved {len(results)} relevant document chunks")
        
        # Generate answer using the RAG results
        if results:
            emit_status("RAG_AGENT", "🤖 Generating comprehensive answer using OpenAI...")
            
            import json
            import re
            try:
                answer_response = generate_answer(
                    query=query,
                    search_results=results
                )
                
                # Parse the JSON response from generate_answer with error handling
                try:
                    answer_data = json.loads(answer_response)
                except json.JSONDecodeError:
                    # Try to fix common JSON issues
                    cleaned_response = re.sub(r'(\d+)_(\d+)', r'\1\2', answer_response)  # Remove underscores from numbers
                    try:
                        answer_data = json.loads(cleaned_response)
                    except json.JSONDecodeError:
                        # If still can't parse, extract answer as string and create basic structure
                        answer_text = answer_response if isinstance(answer_response, str) else "Unable to parse response."
                        answer_data = {
                            "answer": answer_text,
                            "confidence_score": 0.5,
                            "used_context_indices": list(range(len(results))),
                            "used_doc_ids": [result.get("doc_id", "") for result in results]
                        }
                
                # Create detailed citations for RAG
                rag_citations = []
                used_indices = answer_data.get("used_context_indices", [])
                used_doc_ids = answer_data.get("used_doc_ids", [])
                
                for i, (idx, doc_id) in enumerate(zip(used_indices, used_doc_ids)):
                    if idx < len(results):
                        citation = {
                            "citation_id": f"RAG-{i+1}",
                            "document_id": doc_id,
                            "content_preview": results[idx]["text"][:200] + "..." if len(results[idx]["text"]) > 200 else results[idx]["text"],
                            "relevance_score": results[idx].get("score", 0.0),
                            "source_type": "RAG Database",
                            "chunk_index": idx + 1
                        }
                        rag_citations.append(citation)
                
                emit_status("RAG_AGENT", f"✅ RAG analysis complete! Generated answer with {len(rag_citations)} citations")
                
                response_data = {
                    "source": "qdrant",
                    "answer": answer_data.get("answer", "Unable to generate answer from search results."),
                    "confidence_score": answer_data.get("confidence_score", 0.0),
                    "used_context_indices": used_indices,
                    "used_doc_ids": used_doc_ids,
                    "rag_citations": rag_citations,
                    "results": results,
                    "count": len(results),
                    "success": True
                }
                
                # Store response for citation extraction
                store_tool_response(response_data)
                
                return response_data
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse generate_answer response: {e}")
                logger.error(f"Raw response: {answer_response}")
                
                emit_status("RAG_AGENT", "⚠️ JSON parsing failed, using fallback citation method")
                
                # Fallback: use raw response as answer with basic citations
                rag_citations = []
                for i, result in enumerate(results):
                    citation = {
                        "citation_id": f"RAG-{i+1}",
                        "document_id": result.get("doc_id", ""),
                        "content_preview": result["text"][:200] + "..." if len(result["text"]) > 200 else result["text"],
                        "relevance_score": result.get("score", 0.0),
                        "source_type": "RAG Database",
                        "chunk_index": i + 1
                    }
                    rag_citations.append(citation)
                
                emit_status("RAG_AGENT", f"✅ RAG analysis complete with fallback citations ({len(rag_citations)} sources)")
                
                response_data = {
                    "source": "qdrant",
                    "answer": answer_response if isinstance(answer_response, str) else "Unable to generate answer.",
                    "confidence_score": 0.5,
                    "used_context_indices": list(range(len(results))),
                    "used_doc_ids": [result.get("doc_id", "") for result in results],
                    "rag_citations": rag_citations,
                    "results": results,
                    "count": len(results),
                    "success": True
                }
                
                # Store response for citation extraction
                store_tool_response(response_data)
                
                return response_data
        else:
            emit_status("RAG_AGENT", "❌ No relevant documents found in RAG database")
            response_data = {
                "source": "qdrant",
                "answer": "No relevant financial documents found in the database for your query.",
                "confidence_score": 1.0,  # High confidence that no results were found
                "used_context_indices": [],
                "used_doc_ids": [],
                "rag_citations": [],
                "results": [],
                "count": 0,
                "success": True
            }
            
            # Store response for citation extraction
            store_tool_response(response_data)
            
            return response_data
        
    except Exception as e:
        emit_status("RAG_AGENT", f"❌ RAG Agent error: {str(e)}")
        logger.error(f"Error searching RAG database: {str(e)}")
        import traceback
        logger.error(f"RAG search tracebook: {traceback.format_exc()}")
        
        response_data = {
            "source": "qdrant", 
            "answer": f"Error occurred while searching financial documents: {str(e)}",
            "confidence_score": 0.0,
            "used_context_indices": [],
            "used_doc_ids": [],
            "rag_citations": [],
            "results": [], 
            "count": 0,
            "success": False,
            "error": str(e)
        }
        
        # Store response for citation extraction
        store_tool_response(response_data)
        
        return response_data


@financial_agent.tool  
async def search_sec_documents(
    ctx: RunContext[QueryContext],
    query: str,
    ticker: str = None,
    form_type: str = None
) -> Dict[str, Any]:
    """
    Search SEC filings and regulatory documents, then generate a comprehensive answer.
    
    Use this tool for queries about:
    - SEC filings (10-K, 10-Q, 8-K, etc.)
    - Regulatory submissions
    - Company disclosures
    - Recent SEC documents
    - Compliance filings
    
    Args:
        query: Search query for SEC documents
        ticker: Optional company ticker symbol (e.g., "AAPL")
        form_type: Optional SEC form type (e.g., "10-K", "10-Q")
    """
    try:
        emit_status("SEC_AGENT", "🚀 SEC Agent invoked - Searching regulatory filings...")
        
        # Check if SEC API key is available
        sec_api_key = os.getenv("SEC_API_KEY")
        if not sec_api_key:
            emit_status("SEC_AGENT", "❌ SEC API key not configured")
            logger.warning("SEC_API_KEY not found. SEC searches will be limited.")
            
            response_data = {
                "source": "sec_api", 
                "answer": "SEC API key is not configured. Please set the SEC_API_KEY environment variable to search SEC filings.",
                "sec_sources": [],
                "sec_citations": [],
                "results": [],
                "count": 0,
                "success": False,
                "message": "SEC API key not configured. Please set SEC_API_KEY environment variable for SEC filing searches."
            }
            
            # Store response for citation extraction
            store_tool_response(response_data)
            
            return response_data
        
        emit_status("SEC_AGENT", f"🔍 Searching SEC EDGAR database (query: '{query}', ticker: {ticker}, form: {form_type})")
        
        # Try different search strategies if first one fails
        results = []
        
        # Strategy 1: Search as provided
        emit_status("SEC_AGENT", "📋 Strategy 1: Targeted search with all parameters...")
        results = search_sec_filings(
            query=query,
            ticker=ticker,
            form_type=form_type,
            limit=5
        )
        
        # Strategy 2: If no results and ticker provided, try broader search
        if not results and ticker:
            emit_status("SEC_AGENT", "📋 Strategy 2: Broader search - ticker + form type only...")
            results = search_sec_filings(
                query="*",
                ticker=ticker,
                form_type=form_type,
                limit=10
            )
        
        # Strategy 3: If still no results, try without form type restriction
        if not results and ticker:
            emit_status("SEC_AGENT", "📋 Strategy 3: Most broad search - ticker only...")
            results = search_sec_filings(
                query="*",
                ticker=ticker,
                form_type=None,
                limit=10
            )
        
        # Ensure results is a list
        if not isinstance(results, list):
            results = []
        
        emit_status("SEC_AGENT", f"📄 Retrieved {len(results)} SEC filings from EDGAR database")
        
        # Generate answer based on SEC findings
        if results:
            emit_status("SEC_AGENT", "🤖 Analyzing SEC filings and generating comprehensive answer...")
            
            # Format SEC sources for display
            sec_sources = []
            sec_citations = []
            
            for i, result in enumerate(results):
                source = {
                    "title": f"{result.get('form_type', 'Unknown')} - {result.get('company_name', 'Unknown Company')}",
                    "form_type": result.get('form_type', ''),
                    "company_name": result.get('company_name', ''),
                    "ticker": result.get('ticker', ''),
                    "filed_date": result.get('filed_date', ''),
                    "filing_url": result.get('filing_url', ''),
                    "description": result.get('description', ''),
                }
                sec_sources.append(source)
                
                # Create detailed citation
                citation = {
                    "citation_id": f"SEC-{i+1}",
                    "document_type": result.get('form_type', 'Unknown Form'),
                    "company_name": result.get('company_name', 'Unknown Company'),
                    "ticker": result.get('ticker', ''),
                    "accession_number": result.get('accession_number', ''),
                    "filed_date": result.get('filed_date', ''),
                    "period_end_date": result.get('period_end_date', ''),
                    "filing_url": result.get('filing_url', ''),
                    "description": result.get('description', ''),
                    "source_type": "SEC EDGAR Database"
                }
                sec_citations.append(citation)
            
            # Generate a comprehensive answer based on SEC filings found
            answer = f"""Based on my search of SEC filings, I found {len(results)} relevant documents"""
            
            if ticker:
                answer += f" for {ticker.upper()}"
            if form_type:
                answer += f" in {form_type} filings"
                
            answer += ":\n\n"
            
            # Add details about the most relevant filings
            for i, result in enumerate(results[:3], 1):  # Show top 3
                answer += f"{i}. **{result.get('form_type', 'Unknown Form')}** - {result.get('company_name', 'Unknown Company')}\n"
                answer += f"   Filed: {result.get('filed_date', 'Unknown date')}\n"
                if result.get('description'):
                    answer += f"   Description: {result.get('description', '')}\n"
                answer += f"   [View Filing]({result.get('filing_url', '#')})\n\n"
            
            if len(results) > 3:
                answer += f"...and {len(results) - 3} additional filings.\n\n"
            
            # Add specific insights based on the query
            if "working capital" in query.lower():
                answer += "For working capital analysis, focus on the balance sheet and cash flow statements in these filings. Look for current assets, current liabilities, and changes in working capital components."
            elif "cash flow" in query.lower():
                answer += "For cash flow analysis, examine the cash flow statements in these filings, particularly operating cash flow trends and free cash flow generation."
            elif "earnings" in query.lower() or "revenue" in query.lower():
                answer += "For earnings and revenue analysis, review the income statements and management discussion sections in these filings."
            
            emit_status("SEC_AGENT", f"✅ SEC analysis complete! Generated answer with {len(sec_citations)} filing citations")
            
            response_data = {
                "source": "sec_api",
                "answer": answer,
                "sec_sources": sec_sources,
                "sec_citations": sec_citations,
                "results": results,
                "count": len(results),
                "success": True,
                "message": f"Found {len(results)} SEC filings"
            }
            
            # Store response for citation extraction
            store_tool_response(response_data)
            
            return response_data
        else:
            # No results found
            emit_status("SEC_AGENT", "❌ No SEC filings found matching search criteria")
            
            no_results_answer = f"I could not find any SEC filings matching your search criteria"
            if ticker:
                no_results_answer += f" for ticker {ticker.upper()}"
            if form_type:
                no_results_answer += f" with form type {form_type}"
            no_results_answer += ". This could be due to:\n\n"
            no_results_answer += "1. The company may not have filed recent documents matching your criteria\n"
            no_results_answer += "2. The search terms may be too specific\n"
            no_results_answer += "3. There may be a temporary issue with the SEC API\n\n"
            no_results_answer += "Try broadening your search or checking the SEC EDGAR database directly."
            
            response_data = {
                "source": "sec_api",
                "answer": no_results_answer,
                "sec_sources": [],
                "sec_citations": [],
                "results": [],
                "count": 0,
                "success": True,
                "message": "No SEC filings found matching criteria"
            }
            
            # Store response for citation extraction
            store_tool_response(response_data)
            
            return response_data
        
    except Exception as e:
        emit_status("SEC_AGENT", f"❌ SEC Agent error: {str(e)}")
        logger.error(f"Error searching SEC filings: {str(e)}")
        import traceback
        logger.error(f"SEC search traceback: {traceback.format_exc()}")
        
        response_data = {
            "source": "sec_api", 
            "answer": f"An error occurred while searching SEC filings: {str(e)}. Please try again or contact support if the issue persists.",
            "sec_sources": [],
            "sec_citations": [],
            "results": [], 
            "count": 0,
            "success": False,
            "error": str(e),
            "message": "SEC API search failed"
        }
        
        # Store response for citation extraction
        store_tool_response(response_data)
        
        return response_data


async def orchestrate_financial_query(
    query: str,
    user_id: str = "default_user",
    collection_name: str = "documents",
    qdrant_url: str = "http://localhost:6333"
) -> FinancialDataResponse:
    """
    Main orchestrator function that processes a financial query and returns relevant data.
    
    Args:
        query: User's financial query
        user_id: User identifier for RAG filtering
        collection_name: Qdrant collection name
        qdrant_url: Qdrant server URL
        
    Returns:
        FinancialDataResponse with data from the most appropriate source
    """
    try:
        # Clear any previous tool responses
        clear_tool_responses()
        
        context = QueryContext(
            user_id=user_id,
            collection_name=collection_name,
            qdrant_url=qdrant_url
        )
        
        logger.info(f"Processing query: {query}")
        
        # Run the agent to get the basic response
        result = await financial_agent.run(query, deps=context)
        base_response = result.data
        
        # Extract citation data from stored tool responses
        global _tool_responses
        
        # Initialize citation fields
        rag_citations = []
        sec_citations = []
        confidence_score = base_response.confidence_score
        used_context_indices = list(base_response.used_context_indices)
        used_doc_ids = list(base_response.used_doc_ids)
        sec_sources = list(base_response.sec_sources)
        raw_data = []
        
        # Extract citation data from tool responses
        for tool_response in _tool_responses:
            if isinstance(tool_response, dict):
                # RAG citations
                if 'rag_citations' in tool_response and tool_response['rag_citations']:
                    rag_citations.extend(tool_response['rag_citations'])
                    
                # SEC citations
                if 'sec_citations' in tool_response and tool_response['sec_citations']:
                    sec_citations.extend(tool_response['sec_citations'])
                
                # Update other fields from tool responses
                if 'confidence_score' in tool_response and tool_response['confidence_score'] > confidence_score:
                    confidence_score = tool_response['confidence_score']
                    
                if 'used_context_indices' in tool_response and tool_response['used_context_indices']:
                    used_context_indices.extend(tool_response['used_context_indices'])
                    
                if 'used_doc_ids' in tool_response and tool_response['used_doc_ids']:
                    used_doc_ids.extend(tool_response['used_doc_ids'])
                    
                if 'sec_sources' in tool_response and tool_response['sec_sources']:
                    sec_sources.extend(tool_response['sec_sources'])
                
                # Add raw results to data
                if 'results' in tool_response and tool_response['results']:
                    raw_data.extend(tool_response['results'])
        
        # Create enhanced response with preserved citations
        enhanced_response = FinancialDataResponse(
            source=base_response.source,
            answer=base_response.answer,
            query=base_response.query,
            summary=base_response.summary,
            confidence_score=confidence_score,
            used_context_indices=list(set(used_context_indices)),  # Remove duplicates
            used_doc_ids=list(set(used_doc_ids)),  # Remove duplicates
            rag_citations=rag_citations,
            sec_sources=sec_sources,
            sec_citations=sec_citations,
            data=raw_data
        )
        
        # Log citation info for debugging
        logger.info(f"Enhanced response - RAG citations: {len(rag_citations)}, SEC citations: {len(sec_citations)}")
        
        return enhanced_response
        
    except Exception as e:
        logger.error(f"Error in financial query orchestration: {str(e)}")
        return FinancialDataResponse(
            source="error",
            answer=f"I encountered an error while processing your query: {str(e)}. Please try again or rephrase your question.",
            query=query,
            summary="Error occurred during query processing",
            data=[]
        )


# CLI interface for testing
async def main():
    """
    Main function for CLI testing.
    """
    print("Financial Data Orchestrator")
    print("=" * 50)
    
    while True:
        try:
            query = input("\nEnter your financial query (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print(f"\nProcessing: {query}")
            print("-" * 30)
            
            # Process the query
            response = await orchestrate_financial_query(query)
            
            # Display results
            print(f"\nSource: {response.source}")
            print(f"Query: {response.query}")
            
            # Display the main answer
            print(f"\n📋 ANSWER:")
            print("=" * 50)
            print(response.answer)
            print("=" * 50)
            
            # Display RAG-specific metadata if available
            if hasattr(response, 'confidence_score') and response.confidence_score > 0:
                print(f"\n🎯 Confidence Score: {response.confidence_score:.2f}")
                
            if hasattr(response, 'used_doc_ids') and response.used_doc_ids:
                print(f"\n📄 Documents Used: {len(response.used_doc_ids)}")
                for i, doc_id in enumerate(response.used_doc_ids[:3], 1):  # Show first 3
                    print(f"  {i}. {doc_id}")
                if len(response.used_doc_ids) > 3:
                    print(f"  ...and {len(response.used_doc_ids) - 3} more documents")
            
            # Display SEC sources if available
            if hasattr(response, 'sec_sources') and response.sec_sources:
                print(f"\n🏛️ SEC Filing Sources: {len(response.sec_sources)}")
                for i, source in enumerate(response.sec_sources[:3], 1):  # Show first 3
                    print(f"  {i}. {source.get('title', 'Unknown Filing')}")
                    print(f"     Filed: {source.get('filed_date', 'Unknown date')}")
                    print(f"     URL: {source.get('filing_url', 'N/A')}")
                if len(response.sec_sources) > 3:
                    print(f"  ...and {len(response.sec_sources) - 3} more filings")
            
            # Display methodology summary
            if response.summary:
                print(f"\n🔍 Methodology: {response.summary}")
            
            # Display raw data count for transparency
            if hasattr(response, 'data') and response.data:
                print(f"\n📊 Raw Data Points: {len(response.data)} (available for detailed analysis)")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {str(e)}")
    
    print("\nGoodbye!")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main()) 
import asyncio
import time
import json
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import select, update

from api.models.database import (
    UploadedFile, UploadSession, CategorizationStatus, DueDiligenceCategory, 
    LatticeStatus, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON>Result, to_db_id, from_db_id
)
from api.agents.finance_lattice_agent import create_finance_lattice_agent, process_finance_document
from api.agents.legal_lattice_agent import create_legal_lattice_agent, process_legal_document
from api.agents.general_lattice_agent import create_general_lattice_agent, process_general_document
from api.services.pdf_extractor import pdf_extractor
from api.utils.logger import logger


class LatticeAnalysisService:
    """Service for comprehensive lattice analysis with real-time progress tracking"""
    
    def __init__(self, batch_size: int = 5, cell_timeout: int = 30):
        self.batch_size = batch_size
        self.cell_timeout = cell_timeout
        self.pdf_extractor = pdf_extractor
        logger.info(f"🔬 Initialized LatticeAnalysisService with batch_size={batch_size}, timeout={cell_timeout}s")
    
    async def analyze_session_lattice(
        self, 
        session_id: str, 
        general_headers: Optional[List[str]] = None,
        category_headers: Optional[Dict[str, List[str]]] = None,
        db: Session = None
    ):
        """
        Main lattice analysis orchestrator with comprehensive logging
        
        Args:
            session_id: The upload session ID
            general_headers: List of headers to apply to all documents
            category_headers: Dict mapping categories to their specific headers
            db: Database session (optional, will create new if not provided)
        """
        from api.models.database import SessionLocal
        
        # Create new session if not provided (for background tasks)
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        start_time = time.time()
        logger.info(f"🚀 Starting lattice analysis for session {session_id}")
        
        # Log input parameters
        if general_headers:
            logger.info(f"📋 General headers provided: {len(general_headers)} items")
            logger.debug(f"General headers: {general_headers}")
        
        if category_headers:
            total_category_headers = sum(len(headers) for headers in category_headers.values())
            logger.info(f"📂 Category headers provided: {len(category_headers)} categories, {total_category_headers} total headers")
            for category, headers in category_headers.items():
                logger.debug(f"Category '{category}': {headers}")
        
        try:
            # Get session info
            session = db.query(UploadSession).filter(
                UploadSession.id == to_db_id(session_id)
            ).first()
            
            if not session:
                logger.error(f"❌ Session {session_id} not found")
                raise ValueError(f"Session {session_id} not found")
            
            logger.info(f"📊 Session info: {session.company_name or 'Unknown Company'}, {session.total_files} total files")
            
            # Get files ready for lattice analysis (must be categorized first)
            files = db.query(UploadedFile).filter(
                UploadedFile.session_id == to_db_id(session_id),
                UploadedFile.bucket_key.isnot(None),
                UploadedFile.categorization_status == CategorizationStatus.COMPLETED
            ).all()
            
            logger.info(f"📁 Found {len(files)} categorized files ready for lattice analysis")
            
            if len(files) == 0:
                logger.warning(f"⚠️  No categorized files found for session {session_id}")
                session.lattice_status = LatticeStatus.COMPLETED
                session.lattice_completed_at = datetime.now(timezone.utc)
                db.commit()
                return
            
            # Log file distribution by category
            category_counts = {}
            for file in files:
                cat = file.due_diligence_category.value if file.due_diligence_category else "unknown"
                category_counts[cat] = category_counts.get(cat, 0) + 1
            
            logger.info(f"📈 File distribution: {dict(category_counts)}")
            
            # Calculate total matrix cells and prepare file processing plan
            file_processing_plan = []
            total_cells = 0
            
            for file in files:
                headers_for_file = self._get_headers_for_file(
                    file, general_headers, category_headers
                )
                
                if headers_for_file:
                    file_processing_plan.append({
                        "file": file,
                        "headers": headers_for_file,
                        "cell_count": len(headers_for_file)
                    })
                    total_cells += len(headers_for_file)
                    
                    logger.debug(f"File {file.filename}: {len(headers_for_file)} headers to process")
            
            logger.info(f"🔢 Total lattice cells to process: {total_cells} across {len(file_processing_plan)} files")
            
            # Initialize session tracking
            session.lattice_status = LatticeStatus.IN_PROGRESS
            session.lattice_started_at = datetime.now(timezone.utc)
            session.lattice_progress = {
                "total_cells": total_cells,
                "completed_cells": 0,
                "failed_cells": 0,
                "files_processed": 0,
                "total_files": len(file_processing_plan),
                "current_batch": None,
                "processing_rate_cells_per_minute": 0,
                "estimated_completion_time": None
            }
            db.commit()
            
            logger.info(f"✅ Session lattice tracking initialized")
            
            # Process files in batches
            completed_cells = 0
            failed_cells = 0
            
            for i in range(0, len(file_processing_plan), self.batch_size):
                batch = file_processing_plan[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (len(file_processing_plan) + self.batch_size - 1) // self.batch_size
                
                batch_cell_count = sum(item["cell_count"] for item in batch)
                logger.info(f"🔄 Processing batch {batch_num}/{total_batches} with {len(batch)} files ({batch_cell_count} cells)")
                
                # Update current batch info
                session.lattice_progress["current_batch"] = {
                    "batch_number": batch_num,
                    "total_batches": total_batches,
                    "files_in_batch": [item["file"].filename for item in batch],
                    "cells_in_batch": batch_cell_count
                }
                db.commit()
                
                # Process batch
                batch_start = time.time()
                batch_results = await self._process_file_batch(batch, db)
                batch_time = time.time() - batch_start
                
                # Update counters
                batch_completed = sum(r["cells_completed"] for r in batch_results)
                batch_failed = sum(r["cells_failed"] for r in batch_results)
                
                completed_cells += batch_completed
                failed_cells += batch_failed
                
                # Calculate processing rate
                total_time_elapsed = time.time() - start_time
                processing_rate = (completed_cells / total_time_elapsed) * 60 if total_time_elapsed > 0 else 0
                
                # Estimate completion time
                remaining_cells = total_cells - completed_cells - failed_cells
                estimated_minutes = remaining_cells / processing_rate if processing_rate > 0 else None
                estimated_completion = None
                if estimated_minutes:
                    estimated_completion = (datetime.now(timezone.utc) + 
                                          timedelta(minutes=estimated_minutes)).isoformat()
                
                logger.info(f"✅ Batch {batch_num} completed in {batch_time:.2f}s: {batch_completed} success, {batch_failed} failed")
                logger.info(f"⚡ Processing rate: {processing_rate:.1f} cells/minute")
                
                # Update session progress
                session.lattice_progress.update({
                    "completed_cells": completed_cells,
                    "failed_cells": failed_cells,
                    "files_processed": i + len(batch),
                    "processing_rate_cells_per_minute": round(processing_rate, 1),
                    "estimated_completion_time": estimated_completion
                })
                db.commit()
                
                # Log overall progress
                progress_pct = (completed_cells / total_cells) * 100 if total_cells > 0 else 0
                logger.info(f"📊 Overall progress: {progress_pct:.1f}% ({completed_cells}/{total_cells} cells)")
            
            # Finalize session
            total_time = time.time() - start_time
            session.lattice_completed_at = datetime.now(timezone.utc)
            session.lattice_status = LatticeStatus.COMPLETED
            session.lattice_progress["current_batch"] = None
            session.lattice_progress["final_processing_rate"] = round((total_cells / total_time) * 60, 1)
            db.commit()
            
            logger.info(f"🎉 Lattice analysis completed for session {session_id}")
            logger.info(f"⏱️  Total processing time: {total_time:.2f}s ({total_time/60:.1f} minutes)")
            logger.info(f"📈 Final results: {completed_cells} cells completed, {failed_cells} cells failed")
            logger.info(f"🎯 Success rate: {(completed_cells/total_cells*100):.1f}%" if total_cells > 0 else "N/A")
            logger.info(f"⚡ Average rate: {((completed_cells + failed_cells) / total_time) * 60:.1f} cells/minute")
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 Lattice analysis failed for session {session_id} after {total_time:.2f}s")
            logger.error(f"Error details: {str(e)}", exc_info=True)
            
            # Update session with error
            session.lattice_status = LatticeStatus.FAILED
            session.lattice_error_message = str(e)
            session.lattice_completed_at = datetime.now(timezone.utc)
            db.commit()
            raise
            
        finally:
            if should_close_db:
                db.close()
                logger.debug(f"🔧 Database connection closed for session {session_id}")
    
    def _get_headers_for_file(
        self,
        file: UploadedFile,
        general_headers: Optional[List[str]],
        category_headers: Optional[Dict[str, List[str]]]
    ) -> List[Tuple[str, str]]:
        """
        Get all headers that should be processed for a specific file
        
        Args:
            file: The file to process
            general_headers: General headers that apply to all files
            category_headers: Category-specific headers
            
        Returns:
            List of (header, category) tuples
        """
        headers_for_file = []
        
        # Add general headers (apply to all files)
        if general_headers:
            for header in general_headers:
                headers_for_file.append((header, "general"))
        
        # Add category-specific headers
        if category_headers and file.due_diligence_category:
            file_category = file.due_diligence_category.value
            cat_headers = category_headers.get(file_category, [])
            for header in cat_headers:
                headers_for_file.append((header, file_category))
        
        # Remove duplicates while preserving order
        seen = set()
        unique_headers = []
        for header, category in headers_for_file:
            if header not in seen:
                unique_headers.append((header, category))
                seen.add(header)
        
        return unique_headers
    
    async def _process_file_batch(
        self, 
        batch: List[Dict[str, Any]], 
        db: Session
    ) -> List[Dict[str, Any]]:
        """
        Process a batch of files with parallel execution and detailed logging
        
        Args:
            batch: List of file processing plans
            db: Database session
            
        Returns:
            List of batch processing results
        """
        logger.debug(f"🔄 Starting batch processing with {len(batch)} files")
        
        # Create tasks for parallel processing
        tasks = []
        for item in batch:
            task = self._process_single_file_with_headers(
                item["file"], 
                item["headers"], 
                db
            )
            tasks.append(task)
        
        # Execute tasks with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.cell_timeout * len(batch) * 2  # Extra buffer for batch
            )
            
            # Process results and handle exceptions
            batch_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ File processing exception in batch: {str(result)}")
                    batch_results.append({
                        "status": "error",
                        "filename": batch[i]["file"].filename,
                        "cells_completed": 0,
                        "cells_failed": batch[i]["cell_count"],
                        "error": str(result)
                    })
                else:
                    batch_results.append(result)
            
            return batch_results
            
        except asyncio.TimeoutError:
            logger.error(f"⏰ Batch processing timeout after {self.cell_timeout * len(batch) * 2}s")
            return [{
                "status": "timeout",
                "filename": item["file"].filename,
                "cells_completed": 0,
                "cells_failed": item["cell_count"],
                "error": "Batch timeout"
            } for item in batch]
    
    async def _process_single_file_with_headers(
        self,
        file: UploadedFile,
        headers: List[Tuple[str, str]],
        db: Session
    ) -> Dict[str, Any]:
        """
        Process a single file with its assigned headers
        
        Args:
            file: The file to process
            headers: List of (header, category) tuples
            db: Database session
            
        Returns:
            Processing result summary
        """
        start_time = time.time()
        logger.info(f"🔍 Processing file: {file.filename} with {len(headers)} headers")
        logger.debug(f"Headers for {file.filename}: {[h[0] for h in headers]}")
        
        try:
            # Extract PDF content (first page for speed and consistency)
            logger.debug(f"📄 Extracting content from {file.bucket_key}")
            
            # Run PDF extraction in thread pool since it's sync
            loop = asyncio.get_event_loop()
            content = await loop.run_in_executor(
                None, 
                self.pdf_extractor.extract_first_page_text, 
                file.bucket_key
            )
            
            if not content:
                logger.warning(f"⚠️  No content extracted from {file.filename}")
                content = ""
            
            logger.debug(f"📄 Extracted {len(content)} characters from {file.filename}")
            
            # Determine which agent to use based on file category
            file_category = file.due_diligence_category.value if file.due_diligence_category else "unknown"
            agent, agent_type = self._get_agent_for_category(file_category, [h[0] for h in headers])
            
            logger.debug(f"🤖 Using {agent_type} agent for {file.filename}")
            
            # Process with agent
            if agent_type == "finance":
                result = await process_finance_document(
                    agent, content, file.filename, [h[0] for h in headers]
                )
            elif agent_type == "legal":
                result = await process_legal_document(
                    agent, content, file.filename, [h[0] for h in headers]
                )
            else:
                result = await process_general_document(
                    agent, content, file.filename, [h[0] for h in headers], file_category
                )
            
            if result["status"] != "success":
                logger.error(f"❌ Agent processing failed for {file.filename}: {result.get('error', 'Unknown error')}")
                return {
                    "status": "error",
                    "filename": file.filename,
                    "cells_completed": 0,
                    "cells_failed": len(headers),
                    "error": result.get("error", "Agent processing failed")
                }
            
            # Store results in database
            cells_stored = 0
            cells_failed = 0
            
            for header, header_category in headers:
                try:
                    # Get result value from agent response
                    field_name = self._normalize_field_name(header)
                    value = getattr(result["raw_result"].data, field_name, "Not Found")
                    
                    # Determine result type
                    result_type = self._classify_result_type(str(value))
                    
                    # Store in database
                    lattice_result = LatticeResult(
                        session_id=file.session_id,
                        file_id=file.id,
                        header_key=header,
                        header_category=header_category,
                        result_value=str(value),
                        result_type=result_type,
                        agent_type=agent_type,
                        processing_time_ms=int(result.get("agent_time", 0) * 1000),
                        processed_at=datetime.now(timezone.utc)
                    )
                    db.add(lattice_result)
                    cells_stored += 1
                    
                    logger.debug(f"✅ Stored result: {header} = {value}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to store result for {header}: {str(e)}")
                    cells_failed += 1
            
            db.commit()
            
            total_time = time.time() - start_time
            logger.info(f"✅ File {file.filename} completed: {cells_stored} cells stored, {cells_failed} failed in {total_time:.2f}s")
            
            return {
                "status": "success",
                "filename": file.filename,
                "cells_completed": cells_stored,
                "cells_failed": cells_failed,
                "processing_time": total_time,
                "agent_type": agent_type
            }
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ Error processing {file.filename} after {total_time:.2f}s: {str(e)}")
            logger.error(f"Full error details for {file.filename}", exc_info=True)
            
            return {
                "status": "error",
                "filename": file.filename,
                "cells_completed": 0,
                "cells_failed": len(headers),
                "processing_time": total_time,
                "error": str(e)
            }
    
    def _get_agent_for_category(self, category: str, headers: List[str]) -> Tuple[Any, str]:
        """
        Get the appropriate agent for a file category and headers
        
        Args:
            category: File category
            headers: List of headers to process
            
        Returns:
            Tuple of (agent, agent_type)
        """
        if category == "financial_due_diligence":
            return create_finance_lattice_agent(headers), "finance"
        elif category == "legal_due_diligence":
            return create_legal_lattice_agent(headers), "legal"
        else:
            return create_general_lattice_agent(headers), "general"
    
    def _normalize_field_name(self, header: str) -> str:
        """Normalize header to valid Python field name"""
        import re
        field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
        field_name = re.sub(r'_+', '_', field_name).strip('_')
        
        if field_name and field_name[0].isdigit():
            field_name = f"field_{field_name}"
        
        return field_name
    
    def _classify_result_type(self, value: str) -> str:
        """
        Classify the type of result value
        
        Args:
            value: The result value as string
            
        Returns:
            Result type classification
        """
        value_lower = value.lower().strip()
        
        if value_lower in ['not found', 'n/a', '']:
            return "not_found"
        elif value_lower == 'not applicable':
            return "not_applicable"
        else:
            # Try to detect if it looks like a number
            import re
            if re.match(r'^[\d.,]+[%$]?$', value.strip()) or \
               re.match(r'^\$?[\d.,]+[kmb]?$', value.strip().lower()) or \
               re.match(r'^[\d.,]+x$', value.strip().lower()):
                return "numeric"
            else:
                return "text"


# Create singleton instance
lattice_analysis_service = LatticeAnalysisService()


# Utility function for getting lattice matrix
async def get_lattice_matrix(session_id: str, db: Session) -> Dict[str, Any]:
    """
    Get the current lattice matrix for a session
    
    Args:
        session_id: Session ID
        db: Database session
        
    Returns:
        Lattice matrix data
    """
    logger.debug(f"🔍 Building lattice matrix for session {session_id}")
    
    # Get all files and their results
    files = db.query(UploadedFile).filter(
        UploadedFile.session_id == to_db_id(session_id),
        UploadedFile.bucket_key.isnot(None)
    ).all()
    
    # Get all results
    results = db.query(LatticeResult).filter(
        LatticeResult.session_id == to_db_id(session_id)
    ).all()
    
    # Build matrix
    matrix = {}
    all_headers = set()
    
    # Group results by file
    results_by_file = {}
    for result in results:
        file_id = from_db_id(result.file_id)
        if file_id not in results_by_file:
            results_by_file[file_id] = {}
        results_by_file[file_id][result.header_key] = {
            "value": result.result_value,
            "type": result.result_type,
            "agent_type": result.agent_type,
            "processed_at": result.processed_at.isoformat() if result.processed_at else None,
            "processing_time_ms": result.processing_time_ms,
            "status": "completed"
        }
        all_headers.add(result.header_key)
    
    # Get headers from database
    header_records = db.query(LatticeHeader).filter(
        LatticeHeader.session_id == to_db_id(session_id)
    ).all()
    
    for header_record in header_records:
        if isinstance(header_record.headers, str):
            headers = json.loads(header_record.headers)
        else:
            headers = header_record.headers
        
        for header in headers:
            all_headers.add(header)
    
    # Build matrix for each file
    for file in files:
        file_id = from_db_id(file.id)
        file_results = results_by_file.get(file_id, {})
        
        file_row = {}
        for header in all_headers:
            if header in file_results:
                file_row[header] = file_results[header]
            else:
                file_row[header] = {
                    "value": None,
                    "type": None,
                    "agent_type": None,
                    "processed_at": None,
                    "processing_time_ms": None,
                    "status": "pending"
                }
        
        matrix[file.filename] = file_row
    
    logger.debug(f"📊 Built lattice matrix: {len(matrix)} files x {len(all_headers)} headers")
    
    return {
        "matrix": matrix,
        "file_count": len(matrix),
        "header_count": len(all_headers),
        "total_cells": len(matrix) * len(all_headers),
        "headers": sorted(list(all_headers))
    }
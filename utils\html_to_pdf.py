#!/usr/bin/env python3
"""
Enhanced HTML to PDF Converter with WeasyPrint
Supports multiple data sources and provides comprehensive logging and error handling
"""

import pandas as pd
import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import time
import re
import tempfile
import subprocess
import sys
import argparse
import logging
import json
from datetime import datetime
from pathlib import Path
from tqdm import tqdm

# WeasyPrint setup for macOS - this needs to happen before importing WeasyPrint
# because it depends on system libraries that need to be found
if sys.platform == 'darwin':
    os.environ['DYLD_FALLBACK_LIBRARY_PATH'] = '/opt/homebrew/lib:' + os.environ.get('DYLD_FALLBACK_LIBRARY_PATH', '')

from weasyprint import HTML, CSS


class DocumentProcessor:
    """Main class that handles the document processing workflow"""
    
    def __init__(self, args):
        self.args = args
        self.failed_documents = []
        self.successful_conversions = 0
        self.failed_conversions = 0
        self.skipped_aspx_count = 0
        self.skipped_pdf_count = 0
        self.setup_logging()
        
    def setup_logging(self):
        """Set up logging configuration based on user preferences"""
        log_level = logging.DEBUG if self.args.verbose else logging.INFO
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        # Create logs directory if it doesn't exist
        logs_dir = Path(self.args.output_dir) / "logs"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging to both file and console
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(logs_dir / f"conversion_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        
    def sanitize_filename(self, filename):
        """Convert document type to a safe filename - no more weird characters breaking our day"""
        # Remove or replace problematic characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        filename = re.sub(r'[\s]+', '-', filename)  # Replace spaces with hyphens
        filename = filename.lower().strip('-')
        
        # Make sure we don't end up with something crazy long
        if len(filename) > 100:
            filename = filename[:100]
            
        return filename if filename else "unknown-document"
    
    def load_excel_data_mp(self):
        """Load data from the MP Materials Excel file - the original format"""
        excel_file = "MP Materials Due Diligence Document Links.xlsx"
        
        try:
            df = pd.read_excel(excel_file, sheet_name=0)
            self.logger.info(f"Successfully loaded MP Materials Excel file: {excel_file}")
            
            # Extract the specific range we need for MP Materials
            extracted_data = df.iloc[15:38, 1:9]  # Rows 16-38, columns B-I
            
            # Column mappings for MP Materials format
            column_names = [
                "ID", "Category", "Document_Type", "Description", 
                "URL", "PDF_Available", "Date_Reference", "Complete"
            ]
            
            data_list = []
            for index, row in extracted_data.iterrows():
                row_dict = {}
                for i, col_name in enumerate(column_names):
                    value = row.iloc[i]
                    if pd.isna(value):
                        row_dict[col_name] = None
                    else:
                        row_dict[col_name] = str(value) if not isinstance(value, (int, float)) else value
                
                row_dict['Excel_Row'] = index + 1
                data_list.append(row_dict)
            
            self.logger.info(f"Extracted {len(data_list)} rows from MP Materials file")
            return data_list
            
        except Exception as e:
            self.logger.error(f"Failed to load MP Materials Excel file: {str(e)}")
            return []
    
    def load_excel_data_resido(self):
        """Load data from the Resido Excel file - the new kid on the block"""
        excel_file = "DueDiligence docs _ Resido .xlsx"
        
        try:
            df = pd.read_excel(excel_file, sheet_name=0)
            self.logger.info(f"Successfully loaded Resido Excel file: {excel_file}")
            
            # For Resido, data starts at row 22 (0-indexed: 21) and we have 8 columns
            # We'll extract a generous range to make sure we get everything
            extracted_data = df.iloc[21:60, 1:8]  # Should cover all data rows
            
            # Column mappings for Resido format
            column_names = [
                "ID", "Category", "Document_Type", "Description", 
                "URL", "PDF_Available", "Date_Reference"
            ]
            
            data_list = []
            for index, row in extracted_data.iterrows():
                # Skip rows that don't have URLs - they're probably empty or headers
                if pd.isna(row.iloc[4]) or not str(row.iloc[4]).startswith('http'):
                    continue
                    
                row_dict = {}
                for i, col_name in enumerate(column_names):
                    value = row.iloc[i]
                    if pd.isna(value):
                        row_dict[col_name] = None
                    else:
                        row_dict[col_name] = str(value) if not isinstance(value, (int, float)) else value
                
                row_dict['Excel_Row'] = index + 1
                data_list.append(row_dict)
            
            self.logger.info(f"Extracted {len(data_list)} rows from Resido file")
            return data_list
            
        except Exception as e:
            self.logger.error(f"Failed to load Resido Excel file: {str(e)}")
            return []
    
    def create_custom_css(self):
        """Create CSS styling for our PDFs - making them look professional and properly aligned"""
        return CSS(string="""
            /* Page setup with proper margins */
            @page {
                size: A4;
                margin: 1.5cm 1.5cm 1.5cm 1.5cm;
            }
            
            /* AGGRESSIVE CSS Reset - force everything to zero with !important */
            * {
                margin: 0 !important;
                padding: 0 !important;
                text-indent: 0 !important;
                position: static !important;
                left: auto !important;
                right: auto !important;
                top: auto !important;
                bottom: auto !important;
                box-sizing: border-box !important;
            }
            
            html {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }
            
            body {
                font-family: Arial, sans-serif;
                font-size: 10pt;
                line-height: 1.4;
                color: #000;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                /* Prevent any shifting by ensuring consistent font rendering */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            
            /* Force all divs to not have margins */
            div {
                margin: 0 !important;
                padding: 0 !important;
                width: auto !important;
                max-width: 100% !important;
            }
            
            /* Table styling to match original document appearance */
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 0 0 1em 0;
                page-break-inside: auto;
                table-layout: fixed;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 4px 6px;
                text-align: left;
                vertical-align: top;
                word-wrap: break-word;
                font-size: 9pt;
                overflow: hidden;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            tr {
                page-break-inside: avoid;
            }
            
            /* Headers - prevent orphaning and ensure proper spacing */
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid;
                margin: 0.8em 0 0.4em 0;
                padding: 0;
            }
            
            /* Paragraphs - force zero margins except bottom for readability */
            p {
                margin: 0 0 0.5em 0 !important;
                padding: 0 !important;
                text-indent: 0 !important;
            }
            
            /* Lists - minimal indentation for readability */
            ul, ol {
                margin: 0 0 0.5em 1em !important;
                padding: 0 !important;
                list-style-position: inside !important;
            }
            
            li {
                margin: 0 !important;
                padding: 0 !important;
                text-indent: 0 !important;
            }
            
            /* Page breaks */
            .page-break {
                page-break-after: always;
            }
            
            /* Handle those massive tables that break everything */
            .large-table {
                font-size: 8pt;
            }
            
            .large-table th,
            .large-table td {
                padding: 2px 4px;
            }
            
            /* Specific fixes for SEC document formatting */
            .DocumentTitle,
            .DocumentHeader {
                text-align: center;
                font-weight: bold;
                margin: 0.5em 0;
            }
            
            /* Prevent text from getting cut off */
            .text-content {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            
            /* Fix any nested table issues */
            table table {
                margin: 0;
                width: 100%;
            }
            
            /* Ensure images don't cause layout issues */
            img {
                max-width: 100%;
                height: auto;
            }
        """)
    
    def preprocess_html(self, html_content):
        """Clean up HTML content so it doesn't break our PDF generation and prevent layout shifts"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove stuff that causes problems
        for element in soup.find_all(['script', 'style', 'noscript']):
            element.decompose()
        
        # Handle broken images - nobody wants those red X's in their PDFs
        for img in soup.find_all('img'):
            src = img.get('src', '')
            if src and not src.startswith(('http://', 'https://', 'data:')):
                self.logger.debug(f"Removing broken image reference: {src}")
                img.decompose()
        
        # AGGRESSIVE FIX: Find and neutralize SEC document wrappers
        # These divs have width:97% and margin-left:1.5% which cause the shift
        wrapper_divs = soup.find_all('div', style=lambda x: x and 'width' in x and 'margin' in x)
        for wrapper in wrapper_divs:
            style = wrapper.get('style', '')
            if '97%' in style or 'margin-left' in style:
                # Completely remove the wrapper div but keep its contents
                wrapper.unwrap()
                self.logger.debug("Unwrapped SEC document wrapper div")
        
        # Remove ALL inline styles that affect layout - be super aggressive
        for element in soup.find_all():
            if element.get('style'):
                style = element.get('style', '')
                
                # List of style properties to completely remove
                remove_properties = [
                    'margin', 'margin-left', 'margin-right', 'margin-top', 'margin-bottom',
                    'padding', 'padding-left', 'padding-right', 'padding-top', 'padding-bottom',
                    'position', 'left', 'right', 'top', 'bottom',
                    'width', 'min-width', 'max-width',  # Remove width constraints too
                    'text-indent'  # This can also cause content to shift right
                ]
                
                # Remove each property completely
                for prop in remove_properties:
                    # Use more specific regex to handle all cases
                    style = re.sub(rf'{prop}\s*:\s*[^;]+;?', '', style, flags=re.IGNORECASE)
                
                # Clean up any double semicolons or trailing semicolons
                style = re.sub(r';\s*;', ';', style)
                style = re.sub(r';\s*$', '', style)
                style = style.strip()
                
                if style:
                    element['style'] = style
                else:
                    # Remove the style attribute completely if empty
                    del element['style']
        
        # Fix table structures - some websites have really messy tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            if rows:
                # Find the maximum number of columns
                max_cols = max(len(row.find_all(['td', 'th'])) for row in rows)
                
                # Add styling for large tables
                if max_cols > 10:
                    table['class'] = table.get('class', []) + ['large-table']
                
                # Remove any table attributes that might cause alignment issues
                for attr in ['align', 'valign', 'width', 'height']:
                    if table.get(attr):
                        del table[attr]
                
                # Pad rows with empty cells to prevent wonky layouts
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    while len(cells) < max_cols:
                        new_cell = soup.new_tag('td')
                        new_cell.string = ''
                        row.append(new_cell)
                        cells = row.find_all(['td', 'th'])
                    
                    # Clean up cell attributes that might cause shifts
                    for cell in cells:
                        for attr in ['align', 'valign', 'width', 'height']:
                            if cell.get(attr):
                                del cell[attr]
        
        # Remove body styles and handle special divs
        if soup.body:
            if soup.body.get('style'):
                self.logger.debug(f"Removing body inline style: {soup.body.get('style')}")
                del soup.body['style']
            
            # Special handling for the first div with width:8.5in
            first_div = soup.body.find('div')
            if first_div and first_div.get('style') and '8.5in' in first_div.get('style', ''):
                self.logger.debug("Found and unwrapping first div with width:8.5in")
                first_div.unwrap()
        
        # Make sure we have proper HTML structure
        if not soup.html:
            new_soup = BeautifulSoup('<html></html>', 'html.parser')
            new_soup.html.append(soup)
            soup = new_soup
        
        if not soup.head:
            head = soup.new_tag('head')
            soup.html.insert(0, head)
        
        if not soup.body:
            # Move everything that's not a head into body
            body_content = []
            for elem in soup.html.children:
                if elem.name and elem.name != 'head':
                    body_content.append(elem)
            
            body = soup.new_tag('body')
            for elem in body_content:
                elem.extract()
                body.append(elem)
            soup.html.append(body)
        
        # Add UTF-8 encoding and viewport for consistent rendering
        meta_charset = soup.new_tag('meta')
        meta_charset['charset'] = 'utf-8'
        soup.head.insert(0, meta_charset)
        
        # Add viewport meta tag for consistent rendering
        meta_viewport = soup.new_tag('meta')
        meta_viewport['name'] = 'viewport'
        meta_viewport['content'] = 'width=device-width, initial-scale=1.0'
        soup.head.insert(1, meta_viewport)
        
        return str(soup)
    
    def fetch_url_content(self, url):
        """Fetch HTML content from a URL - being respectful to servers"""
        self.logger.debug(f"Fetching URL: {url}")
        
        # Different sites need different approaches
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        
        if 'sec.gov' in domain:
            # SEC.gov is picky about headers
            headers = {
                'User-Agent': 'Sample Company Name AdminContact@<sample company domain>.com',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Host': 'www.sec.gov'
            }
        else:
            # Generic headers for other sites
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        
        session = requests.Session()
        session.headers.update(headers)
        
        try:
            # Be nice to servers - don't hammer them
            time.sleep(1)
            
            response = session.get(url, timeout=30, allow_redirects=True)
            self.logger.debug(f"Status: {response.status_code}, Content length: {len(response.text)}")
            
            if response.status_code == 200:
                # Check if the content is actually HTML
                content_type = response.headers.get('content-type', '').lower()
                
                # Some sites return PDFs or other binary content
                if 'application/pdf' in content_type:
                    self.logger.warning(f"URL returns PDF content, not HTML: {url}")
                    return None
                
                # Check if it's text/html or similar
                if 'text/html' in content_type or 'text/plain' in content_type or not content_type:
                    # Also do a quick check for HTML-like content
                    text_sample = response.text[:1000].lower()
                    if '<html' in text_sample or '<body' in text_sample or '<div' in text_sample or '<p' in text_sample:
                        return response.text
                    else:
                        self.logger.warning(f"Content doesn't appear to be HTML: {url}")
                        return None
                else:
                    self.logger.warning(f"Unexpected content type '{content_type}' for {url}")
                    return None
            else:
                self.logger.warning(f"HTTP {response.status_code} for {url}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to fetch {url}: {str(e)}")
            return None
    
    def convert_html_to_pdf(self, html_content, output_path):
        """Convert HTML to PDF using WeasyPrint"""
        try:
            # Clean up the HTML first
            processed_html = self.preprocess_html(html_content)
            
            # Create the PDF with our custom styling
            custom_css = self.create_custom_css()
            html_doc = HTML(string=processed_html)
            html_doc.write_pdf(output_path, stylesheets=[custom_css])
            
            self.logger.debug(f"Successfully created PDF: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"PDF conversion failed for {output_path}: {str(e)}")
            return False
    
    def generate_pdf_filename(self, document_type, doc_id, used_filenames):
        """Generate a unique filename based on document type"""
        base_filename = self.sanitize_filename(document_type)
        
        # Handle duplicates by adding numbers
        if base_filename in used_filenames:
            counter = 2
            while f"{base_filename}-{counter}" in used_filenames:
                counter += 1
            filename = f"{base_filename}-{counter}"
        else:
            filename = base_filename
            
        used_filenames.add(filename)
        return f"{filename}.pdf"
    
    def save_failed_documents(self):
        """Save details about failed conversions for debugging"""
        if not self.failed_documents:
            return
            
        failed_file = Path(self.args.output_dir) / "failed_documents.json"
        
        try:
            with open(failed_file, 'w') as f:
                json.dump(self.failed_documents, f, indent=2, default=str)
            self.logger.info(f"Failed documents list saved to: {failed_file}")
        except Exception as e:
            self.logger.error(f"Failed to save failed documents list: {str(e)}")
    
    def process_documents(self):
        """Main processing workflow - this is where the magic happens"""
        self.logger.info(f"Starting HTML to PDF conversion in '{self.args.mode}' mode")
        
        # Load data based on mode
        if self.args.mode == 'resido':
            data = self.load_excel_data_resido()
        else:
            data = self.load_excel_data_mp()
        
        if not data:
            self.logger.error("No data loaded - check your Excel file")
            return
        
        # Filter URLs - skip .aspx and .pdf files, try everything else
        html_data = []
        skipped_aspx = []
        skipped_pdf = []
        
        for row in data:
            url = row.get('URL', '')
            if url and row.get('ID') and str(row['ID']).strip() not in ['nan', '']:
                doc_type = row.get('Document_Type', 'Unknown')
                doc_id = row.get('ID', 'Unknown')
                
                # Check if it's an .aspx file - we don't want those
                if url.lower().endswith('.aspx'):
                    skipped_aspx.append({
                        'id': doc_id,
                        'type': doc_type,
                        'url': url
                    })
                    self.logger.warning(f"⚠️  Skipping .aspx URL - Document {doc_id}: {doc_type}")
                    continue
                
                # Check if it's a direct PDF link - no need to convert
                if url.lower().endswith('.pdf'):
                    skipped_pdf.append({
                        'id': doc_id,
                        'type': doc_type,
                        'url': url
                    })
                    self.logger.info(f"📄 Skipping direct PDF link - Document {doc_id}: {doc_type}")
                    continue
                
                # Process everything else - we'll try to fetch HTML from any URL
                html_data.append(row)
                self.logger.debug(f"Will attempt to process: {doc_type} - {url[:50]}...")
        
        # Report what we found and what we skipped
        self.logger.info(f"Found {len(html_data)} documents to process (will attempt HTML extraction)")
        
        if skipped_aspx:
            self.logger.info(f"Skipped {len(skipped_aspx)} .aspx documents:")
            for doc in skipped_aspx:
                self.logger.info(f"  - {doc['id']}: {doc['type']}")
        
        if skipped_pdf:
            self.logger.info(f"Skipped {len(skipped_pdf)} direct PDF links:")
            for doc in skipped_pdf:
                self.logger.info(f"  - {doc['id']}: {doc['type']}")
        
        # Store skipped counts for summary
        self.skipped_aspx_count = len(skipped_aspx)
        self.skipped_pdf_count = len(skipped_pdf)
        
        if not html_data:
            self.logger.warning("No HTML documents found to process")
            return
        
        # Test mode - just process the first one
        if self.args.test:
            html_data = html_data[:1]
            self.logger.info("Test mode enabled - processing first document only")
        
        # Create output directory
        output_dir = Path(self.args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Keep track of used filenames to avoid duplicates
        used_filenames = set()
        
        # Process each document with a nice progress bar
        for row in tqdm(html_data, desc="Processing Documents", unit="doc"):
            doc_id = row.get('ID', 'unknown')
            doc_type = row.get('Document_Type', 'unknown-document')
            url = row.get('URL')
            
            self.logger.info(f"Processing document {doc_id}: {doc_type}")
            
            # Fetch the HTML content
            html_content = self.fetch_url_content(url)
            
            if html_content:
                # Generate filename based on document type
                pdf_filename = self.generate_pdf_filename(doc_type, doc_id, used_filenames)
                pdf_path = output_dir / pdf_filename
                
                # Convert to PDF
                if self.convert_html_to_pdf(html_content, pdf_path):
                    self.successful_conversions += 1
                    self.logger.info(f"✓ Successfully converted: {pdf_filename}")
                else:
                    self.failed_conversions += 1
                    self.failed_documents.append({
                        'id': doc_id,
                        'document_type': doc_type,
                        'url': url,
                        'error': 'PDF conversion failed',
                        'timestamp': datetime.now().isoformat()
                    })
                    self.logger.error(f"✗ Failed to convert: {doc_type}")
            else:
                self.failed_conversions += 1
                self.failed_documents.append({
                    'id': doc_id,
                    'document_type': doc_type,
                    'url': url,
                    'error': 'Failed to fetch HTML content',
                    'timestamp': datetime.now().isoformat()
                })
                self.logger.error(f"✗ Failed to fetch: {doc_type}")
        
        # Save failed documents for debugging
        self.save_failed_documents()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print a nice summary of what we accomplished"""
        print("\n" + "=" * 60)
        print("🎉 CONVERSION SUMMARY")
        print("=" * 60)
        print(f"✅ Successful conversions: {self.successful_conversions}")
        print(f"❌ Failed conversions: {self.failed_conversions}")
        print(f"⏭️  Skipped .aspx documents: {self.skipped_aspx_count}")
        print(f"📄 Skipped direct PDF links: {self.skipped_pdf_count}")
        print(f"📁 Total documents processed: {self.successful_conversions + self.failed_conversions}")
        print(f"💾 PDFs saved to: {self.args.output_dir}")
        
        if self.failed_conversions > 0:
            print(f"📋 Failed documents details: {Path(self.args.output_dir) / 'failed_documents.json'}")
        
        if self.skipped_aspx_count > 0:
            print(f"ℹ️  Note: .aspx URLs were skipped as they are dynamic pages")
        
        if self.skipped_pdf_count > 0:
            print(f"ℹ️  Note: Direct PDF links were skipped (already in PDF format)")
        
        print("=" * 60)
        
        if self.successful_conversions > 0:
            print("🎊 Great job! Your documents are ready.")
        else:
            print("😞 No documents were converted successfully. Check the logs for details.")


def main():
    """Main entry point - set up CLI arguments and run the processor"""
    parser = argparse.ArgumentParser(
        description="Convert HTML documents to PDF with style and grace",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test with first Resido document
  python html_to_pdf.py --mode resido --test --verbose
  
  # Process all MP Materials documents
  python html_to_pdf.py --mode mp --verbose
  
  # Process all Resido documents with custom output
  python html_to_pdf.py --mode resido --output-dir ./custom_pdfs
        """
    )
    
    parser.add_argument(
        '--mode',
        choices=['mp', 'resido'],
        default='mp',
        help='Which Excel file to process (default: mp)'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='Test mode - process only the first document'
    )
    
    parser.add_argument(
        '--output-dir',
        default='mp_materials/pdfs',
        help='Output directory for PDFs (default: mp_materials/pdfs)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Adjust default output directory based on mode
    if args.output_dir == 'mp_materials/pdfs' and args.mode == 'resido':
        args.output_dir = 'resido/pdfs'
    
    # Create and run the processor
    processor = DocumentProcessor(args)
    processor.process_documents()


if __name__ == "__main__":
    main() 
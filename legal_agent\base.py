import asyncio
import os
import sys
from pathlib import Path
from typing import Optional, List
import openai
from dotenv import load_dotenv

# Add the parent directory to the path to import from utils
sys.path.append(str(Path(__file__).parent.parent))

from utils.pdf_to_markdown import convert_pdf_to_markdown_jina, convert_pdf_to_markdown_pymupdf4llm, convert_pdf_to_markdown_llamaparse, ParserType
from .prompts import EXTRACTION_PROMPT, REDUCTION_PROMPT

# Load API keys from .env
load_dotenv()
agentic_api_key = os.getenv("AGENTIC_API_KEY")
agentic_base_url = os.getenv("AGENTIC_BASE_URL")



def split_markdown_into_pages(md_text):
    """Split markdown text into pages and return list of (page_number, page_content) tuples"""
    pages = [page.strip() for page in md_text.split('================================================================================') if page.strip()]
    return [(i + 1, page) for i, page in enumerate(pages)]

async def extract_fields_from_page(page_text, query, page_number):
    """Extract legal information from a single page"""
    client = openai.AsyncOpenAI(
        api_key=agentic_api_key,
        base_url=agentic_base_url,
        max_retries=3,
        timeout=60.0
    )

    prompt = EXTRACTION_PROMPT.format(query=query, page_number=page_number)
    context = page_text
    
    try:
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": f"You are an expert legal analyst analyzing Page {page_number} of a larger legal document. Be extraordinarily careful and thorough. Think deeply about whether any information on this page, even if partial or incomplete, helps answer the query. Stay strictly grounded to the document content - do not add general legal knowledge or assumptions. Focus on answering the specific query with exact legal facts, figures, and details from the document. Do not miss any relevant information. If this page doesn't contain relevant information, respond with exactly 'Not Found'."},
                {"role": "user", "content": prompt + context}
            ],
            max_tokens=4000,
            temperature=0.3
        )
        result = response.choices[0].message.content.strip()
        
        # Print processing status
        if result == "Not Found":
            print(f"Page {page_number}: Not Found")
        else:
            print(f"Page {page_number}: Found relevant content ({len(result)} chars)")
        
        return result
    except Exception as e:
        print(f"Error processing page {page_number}: {e}")
        return "Not Found"

async def reduce_results_by_document(page_results, query):
    """Combine results from multiple pages into a single analysis"""
    if not page_results:
        return "Not Found"
    
    # Filter out "Not Found" results
    valid_results = [(page_num, result) for page_num, result in page_results if result != "Not Found"]
    
    if not valid_results:
        return "Not Found"
    
    # If only one valid result, return it directly
    if len(valid_results) == 1:
        return valid_results[0][1]
    
    # Configure OpenAI client
    client = openai.AsyncOpenAI(
        api_key=agentic_api_key,
        base_url=agentic_base_url,
        max_retries=3,
        timeout=60.0
    )
    
    # Prepare the page results for the prompt
    page_results_text = "\n\n---\n\n".join([f"Page {page_num} Results:\n{result}" for page_num, result in valid_results])
    
    prompt = REDUCTION_PROMPT.format(query=query, page_results=page_results_text)
    
    try:
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are an expert legal analyst. Be extraordinarily careful and thorough. Your goal is to provide a comprehensive, detailed, and factually accurate answer to the specific query by creating a cohesive analysis from ALL the different page results. Include all individual answers from all pages - only omit information if you are absolutely sure it's totally irrelevant to the query. Stay strictly grounded to the document content - do not add general legal knowledge or assumptions. Reconcile any contradictory information and ensure no relevant details are missed."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error combining results: {e}")
        # Fallback: concatenate all results
        return "\n\n---\n\n".join([f"**Page {page_num}:**\n{result}" for page_num, result in valid_results])

async def legal_agent(
    pdf_path: str,
    lattice_header: str,
    on_disk: bool = True,
    output_dir: str = "legal_agent/output",
    parser_types: Optional[List[ParserType]] = None,
    api_key: Optional[str] = None,
    llamaparse_api_key: Optional[str] = None
) -> str:
    """
    Legal agent function that processes a PDF document and extracts legal information
    
    Args:
        pdf_path (str): Path to the PDF file
        lattice_header (str): The query/question to ask about the document
        on_disk (bool): Whether to save markdown to disk (default: False)
        output_dir (str): Directory to save markdown files if on_disk=True
        parser_types (List[ParserType], optional): List of parsers to use. Defaults to [ParserType.JINA]
        api_key (str, optional): Jina AI API key
        llamaparse_api_key (str, optional): LlamaParse API key
    
    Returns:
        str: The extracted legal analysis
    """
    
    # Set default parsers if none provided
    if parser_types is None:
        parser_types = [ParserType.JINA]
    
    # Step 1: Check for existing markdown or convert PDF to Markdown
    markdown_content = None
    
    # Check if markdown already exists on disk
    if on_disk:
        Path(output_dir).mkdir(exist_ok=True)
        pdf_filename = Path(pdf_path).stem
        markdown_path = Path(output_dir) / f"{pdf_filename}.md"
        
        print(f"Checking for existing markdown at: {markdown_path}")
        print(f"File exists: {markdown_path.exists()}")
        
        if markdown_path.exists():
            print(f"Loading existing markdown from {markdown_path}")
            try:
                with open(markdown_path, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()
                    
                if len(markdown_content) > 50:
                    print(f"Successfully loaded existing markdown ({len(markdown_content)} characters)")
                else:
                    print("Existing markdown file is too short, will re-process PDF")
                    markdown_content = None
                    
            except Exception as e:
                print(f"Error reading existing markdown: {str(e)}, will re-process PDF")
                markdown_content = None
        else:
            print("No existing markdown found, will convert PDF")
    
    # Convert PDF to markdown if not loaded from disk
    if not markdown_content:
        print("Converting PDF to markdown...")
        
        # Try each parser in order until one succeeds
        for parser_type in parser_types:
            try:
                if parser_type == ParserType.JINA:
                    markdown_content = convert_pdf_to_markdown_jina(pdf_path, api_key)
                elif parser_type == ParserType.PYMUPDF4LLM:
                    markdown_content = convert_pdf_to_markdown_pymupdf4llm(pdf_path)
                elif parser_type == ParserType.LLAMAPARSE:
                    markdown_content = await convert_pdf_to_markdown_llamaparse(pdf_path, llamaparse_api_key)
                
                if markdown_content and len(markdown_content) > 50:
                    break
                    
            except Exception as e:
                print(f"Error with {parser_type.value}: {str(e)}")
                continue
        
        if not markdown_content or len(markdown_content) <= 50:
            raise ValueError("Failed to convert PDF to markdown")
        
        # Save to disk if requested
        if on_disk:
            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            print(f"Saved markdown to {markdown_path}")
    
    # Step 2: Split markdown into pages
    pages_with_numbers = split_markdown_into_pages(markdown_content)
    
    if len(pages_with_numbers) == 0:
        raise ValueError("No pages found after splitting markdown")
    
    # Step 3: Extract fields from each page in parallel
    print(f"Analyzing {len(pages_with_numbers)} pages for query: '{lattice_header}'")
    
    semaphore = asyncio.Semaphore(50)
    
    async def process_with_semaphore(page_num, page_content):
        async with semaphore:
            result = await extract_fields_from_page(page_content, lattice_header, page_num)
            return (page_num, result)
    
    # Process pages concurrently
    tasks = [process_with_semaphore(page_num, page_content) for page_num, page_content in pages_with_numbers]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Filter out any exceptions
    valid_results = [r for r in results if not isinstance(r, Exception)]
    
    # Step 4: Combine results
    print(f"Processing {len(valid_results)} page results...")
    
    # Count valid vs not found results
    not_found_count = sum(1 for _, result in valid_results if result.strip() == "Not Found")
    valid_count = len(valid_results) - not_found_count
    print(f"Found relevant information on {valid_count} pages, {not_found_count} pages returned 'Not Found'")
    
    # Show which pages had relevant content
    found_page_numbers = [page_num for page_num, result in valid_results if result.strip() != "Not Found"]
    if found_page_numbers:
        print(f"Pages with relevant content: {sorted(found_page_numbers)}")
    
    final_result = await reduce_results_by_document(valid_results, lattice_header)
    
    # Validate citations against page results
    if final_result and final_result.strip() != "Not Found":
        # Extract pages that had relevant content
        found_pages = {page_num for page_num, result in valid_results if result.strip() != "Not Found"}
        not_found_pages = {page_num for page_num, result in valid_results if result.strip() == "Not Found"}
        
        # Check for citation consistency
        import re
        cited_pages = set()
        for match in re.finditer(r'\[Page (\d+)\]', final_result):
            cited_pages.add(int(match.group(1)))
        
        # Report citation validation
        inconsistent_citations = cited_pages.intersection(not_found_pages)
        unused_found_pages = found_pages - cited_pages
        
        if inconsistent_citations:
            print(f"⚠️  WARNING: Citations to pages that returned 'Not Found': {sorted(inconsistent_citations)}")
        
        if unused_found_pages:
            print(f"💡 INFO: Pages with relevant content not cited: {sorted(unused_found_pages)}")
        
        if not inconsistent_citations and not unused_found_pages:
            print("✅ Citation validation: All citations consistent with page results")
    
    # Print truncated result for preview
    if final_result and final_result.strip() != "Not Found":
        truncated = final_result[:500] + "..." if len(final_result) > 500 else final_result
        print(f"\nResult Preview ({len(final_result)} characters):")
        print("-" * 50)
        print(truncated)
        print("-" * 50)
    else:
        print(f"\nFinal Result: {final_result}")
    
    return final_result 


if __name__ == "__main__":
    print(asyncio.run(legal_agent("mp_materials/pdfs/contract.pdf", "What are the Governing Law & Arbitration Consistency")))
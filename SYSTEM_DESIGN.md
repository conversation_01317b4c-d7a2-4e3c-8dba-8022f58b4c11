# Quantera Document Processing System - Complete Architecture

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Control Flows](#control-flows)
6. [Key Services](#key-services)
7. [Development Features](#development-features)
8. [Error Handling & Recovery](#error-handling--recovery)

## System Overview

The Quantera Document Processing System is a FastAPI-based application designed for:
- **Bulk document uploads** (100s of files)
- **Cloud storage** using DigitalOcean Spaces (S3-compatible)
- **AI-powered document categorization** for due diligence
- **Parallel processing** capabilities
- **Real-time progress tracking**

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Client (REST)  │────▶│  FastAPI App   │────▶│  PostgreSQL DB  │
│                 │     │                 │     │                 │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 │
                        ┌────────▼────────┐
                        │                 │
                        │ DigitalOcean    │
                        │ Spaces (S3)     │
                        │                 │
                        └────────┬────────┘
                                 │
                        ┌────────▼────────┐
                        │                 │
                        │ Pydantic AI     │
                        │ (OpenAI)        │
                        │                 │
                        └─────────────────┘
```

## Architecture Components

### 1. **FastAPI Application** (`api/main.py`)
- Main entry point
- CORS middleware configuration
- Router registration
- Lifespan management

### 2. **Database Layer** (`api/models/database.py`)
- PostgreSQL with SQLAlchemy ORM
- UUID support for PostgreSQL
- String IDs for SQLite (dev mode)
- Connection pooling
- Timezone-aware datetime handling

### 3. **Storage Service** (`api/services/storage.py`)
- DigitalOcean Spaces integration
- S3-compatible API using boto3
- Presigned URL generation
- File management (upload, delete, list)

### 4. **AI Agent** (`api/agents/due_diligence_agent.py`)
- Pydantic AI integration
- Structured output with `CategorizationResult`
- 5 due diligence categories + unknown
- Confidence scoring
- Reasoning and key indicators

### 5. **Parallel Processing** (`api/services/parallel_categorization.py`)
- Asyncio-based batch processing
- Configurable batch size (default: 5)
- Timeout handling
- Progress tracking
- Error recovery

## Database Schema

### Tables

#### 1. **upload_sessions**
```sql
- id: UUID (primary key)
- user_id: String
- company_name: String
- session_type: String
- created_at: DateTime
- updated_at: DateTime
- status: Enum (UploadStatus)
- total_files: Integer
- processed_files: Integer
- total_size_bytes: BigInteger
- session_metadata: JSONB
- error_message: Text
- categorization_total: Integer
- categorization_completed: Integer
- categorization_failed: Integer
- categorization_started_at: DateTime
- categorization_completed_at: DateTime
```

#### 2. **uploaded_files**
```sql
- id: UUID (primary key)
- session_id: UUID (foreign key)
- filename: String
- bucket_key: String (unique)
- bucket_url: String
- file_size: BigInteger
- content_type: String
- file_hash: String (indexed)
- status: Enum (FileStatus)
- created_at: DateTime
- processed_at: DateTime
- file_metadata: JSONB
- error_message: Text
- due_diligence_category: Enum (DueDiligenceCategory)
- categorization_confidence: Decimal(3,2)
- categorization_metadata: JSONB
- categorized_at: DateTime
- categorization_status: Enum (CategorizationStatus)
- categorization_started_at: DateTime
- agent_insights: JSONB
```

#### 3. **file_processing_results**
```sql
- id: UUID (primary key)
- file_id: UUID (foreign key)
- processing_type: String
- extracted_text: Text
- ai_summary: Text
- entities: JSONB
- classifications: JSONB
- embeddings_generated: Boolean
- qdrant_point_ids: JSONB
- processing_metadata: JSONB
- created_at: DateTime
```

### Enums

- **UploadStatus**: PENDING, PROCESSING, COMPLETED, FAILED
- **FileStatus**: PENDING, UPLOADED, PROCESSING, PROCESSED, FAILED
- **DueDiligenceCategory**: FINANCIAL, TECHNICAL, LEGAL, OPERATIONAL, MARKET, UNKNOWN
- **CategorizationStatus**: PENDING, IN_PROGRESS, COMPLETED, FAILED

## API Endpoints

### Upload Endpoints (`/api/v1/upload`)

#### 1. **Create Upload Session**
```
POST /upload/session
Body: {
  "user_id": "string",
  "company_name": "string",
  "session_type": "string",
  "metadata": {}
}
Response: {
  "session_id": "uuid",
  "presigned_urls": [{
    "filename": "string",
    "upload_url": "string",
    "file_id": "uuid"
  }]
}
```

#### 2. **Confirm File Upload**
```
POST /upload/confirm/{file_id}
Body: {
  "file_size": integer,
  "content_type": "string",
  "etag": "string"
}
Response: {
  "status": "confirmed",
  "file_id": "uuid"
}
```

#### 3. **Get Session Details**
```
GET /upload/session/{session_id}
Response: {
  "session_id": "uuid",
  "status": "string",
  "total_files": integer,
  "uploaded_files": integer,
  "files": [file details]
}
```

### Process Endpoints (`/api/v1/process`)

#### 1. **Start Categorization**
```
POST /process/{session_id}
Response: {
  "session_id": "uuid",
  "status": "started",
  "message": "Started categorization for X files",
  "files_to_process": integer,
  "poll_endpoint": "/process/{session_id}/status"
}
```

#### 2. **Get Categorization Status**
```
GET /process/{session_id}/status
Response: {
  "session_id": "uuid",
  "status": "in_progress|completed|not_started",
  "progress_percentage": float,
  "file_status_counts": {
    "pending": integer,
    "in_progress": integer,
    "completed": integer,
    "failed": integer
  },
  "category_distribution": {
    "financial_due_diligence": integer,
    "legal_due_diligence": integer,
    ...
  },
  "files": [file status details]
}
```

#### 3. **Get Files by Category**
```
GET /process/{session_id}/category/{category}
Response: {
  "session_id": "uuid",
  "category": "string",
  "total_files": integer,
  "files": [{
    "filename": "string",
    "bucket_key": "string",
    "categorization_confidence": float,
    "insights": {
      "reasoning": "string",
      "key_indicators": ["string"],
      "usage": {}
    }
  }]
}
```

### Storage Endpoints (`/api/v1/storage`)

#### 1. **List Files**
```
GET /storage/files?prefix=string&limit=integer
Response: {
  "files": [{
    "key": "string",
    "size": integer,
    "last_modified": "datetime"
  }],
  "total": integer
}
```

#### 2. **Delete File**
```
DELETE /storage/files/{key}
Response: {
  "deleted": true,
  "key": "string"
}
```

## Control Flows

### 1. **Document Upload Flow**

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant DB
    participant S3
    
    Client->>FastAPI: POST /upload/session
    FastAPI->>DB: Create session & file records
    FastAPI->>S3: Generate presigned URLs
    FastAPI-->>Client: Return session_id & URLs
    
    Client->>S3: Direct upload files
    S3-->>Client: Upload confirmation
    
    Client->>FastAPI: POST /upload/confirm/{file_id}
    FastAPI->>DB: Update file status
    FastAPI-->>Client: Confirmation response
```

### 2. **Categorization Flow**

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant BackgroundTask
    participant DB
    participant S3
    participant AI
    
    Client->>FastAPI: POST /process/{session_id}
    FastAPI->>DB: Check session status
    FastAPI->>BackgroundTask: Start categorization
    FastAPI-->>Client: Return job started
    
    loop For each batch of 5 files
        BackgroundTask->>DB: Mark files IN_PROGRESS
        BackgroundTask->>S3: Download PDFs
        BackgroundTask->>AI: Categorize (parallel)
        AI-->>BackgroundTask: Categories & insights
        BackgroundTask->>DB: Update results
    end
    
    BackgroundTask->>DB: Mark session complete
    
    Client->>FastAPI: GET /process/{session_id}/status
    FastAPI->>DB: Get current status
    FastAPI-->>Client: Return progress
```

### 3. **Development Mode Flow**

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant DevCache
    participant LocalFiles
    participant S3
    
    Client->>FastAPI: POST /upload/dev/process
    FastAPI->>DevCache: Check existing session
    
    alt Session exists
        FastAPI-->>Client: Return cached session_id
    else New session
        FastAPI->>LocalFiles: Read mp_materials/resido
        FastAPI->>S3: Upload files
        FastAPI->>DevCache: Save session
        FastAPI-->>Client: Return new session_id
    end
```

## Key Services

### 1. **PDFExtractorService** (`api/services/pdf_extractor.py`)
- **Purpose**: Extract text from PDFs stored in S3
- **Features**:
  - Dual library support (pdfplumber, PyPDF2)
  - 24-hour caching system
  - First-page extraction for categorization
  - Full document extraction support

### 2. **ParallelCategorizationService** (`api/services/parallel_categorization.py`)
- **Purpose**: Categorize documents in parallel batches
- **Features**:
  - Batch size: 5 documents
  - Timeout: 30 seconds per document
  - Progress tracking
  - Error recovery
  - Automatic session reset for stuck jobs (>10 minutes)

### 3. **DevSessionCache** (`api/services/dev_cache.py`)
- **Purpose**: Cache development sessions to avoid re-uploads
- **Features**:
  - Persistent JSON storage
  - Session deduplication
  - File tracking

## Development Features

### 1. **Local File Processing**
- Reads from `mp_materials/` and `resido/` directories
- Automatically uploads to S3
- Creates trackable sessions

### 2. **Caching Systems**
- **PDF Extraction Cache**: 24-hour TTL, reduces S3 calls
- **Dev Session Cache**: Persistent, avoids re-uploads
- **Location**: `.cache/` directory

### 3. **Migration Scripts**
- Database schema updates
- Data migrations
- Reset scripts for testing

## Error Handling & Recovery

### 1. **Stuck Session Detection**
- Sessions running >10 minutes are considered stuck
- Automatic reset on next POST request
- Files marked IN_PROGRESS → PENDING

### 2. **Failed Categorization**
- Individual file failures don't stop batch
- Failed files can be retried
- Detailed error logging with insights

### 3. **Timezone Handling**
- All timestamps use `timezone.utc`
- Consistent timezone-aware datetime objects
- No naive datetime comparisons

### 4. **Database Compatibility**
- UUID handling for PostgreSQL
- String IDs for SQLite
- Conversion helpers: `to_db_id()`, `from_db_id()`

## Response Examples

### Successful Categorization Response
```json
{
  "session_id": "188c1893-ebdf-2f5b-b9d4-60208c451927",
  "status": "completed",
  "progress_percentage": 100.0,
  "file_status_counts": {
    "pending": 0,
    "in_progress": 0,
    "completed": 65,
    "failed": 0
  },
  "category_distribution": {
    "financial_due_diligence": 20,
    "legal_due_diligence": 15,
    "operational_due_diligence": 10,
    "technical_due_diligence": 5,
    "market_due_diligence": 10,
    "unknown": 5
  },
  "files": [{
    "filename": "Annual Report.pdf",
    "categorization_status": "completed",
    "category": "financial_due_diligence",
    "confidence": 0.95,
    "insights": {
      "reasoning": "This document contains comprehensive financial statements...",
      "key_indicators": ["balance sheet", "income statement", "cash flow"],
      "usage": {
        "requests": 1,
        "request_tokens": 1250,
        "response_tokens": 350,
        "total_tokens": 1600
      }
    }
  }]
}
```

### Category Query Response
```json
{
  "session_id": "188c1893-ebdf-2f5b-b9d4-60208c451927",
  "category": "legal_due_diligence",
  "total_files": 15,
  "files": [{
    "filename": "employment-agreement.pdf",
    "file_size": 64075,
    "content_type": "application/pdf",
    "bucket_key": "uploads/session-id/pdfs/employment-agreement.pdf",
    "categorization_confidence": 0.92,
    "categorized_at": "2025-07-15T14:30:00Z",
    "insights": {
      "reasoning": "This is an employment agreement containing legal terms...",
      "key_indicators": ["employment terms", "legal agreement", "contract"],
      "usage": {...}
    }
  }]
}
```

## Performance Characteristics

- **Upload Speed**: Limited by client bandwidth to S3
- **Categorization Speed**: 5 documents parallel, ~5-10 seconds per batch
- **Database Queries**: Optimized with composite indexes
- **Caching**: Reduces S3 calls by ~80% for repeated operations
- **Memory Usage**: Streaming for large files, no full file loading

## Security Considerations

- Presigned URLs expire after 1 hour
- No direct file access through API
- Database credentials in environment variables
- S3 bucket policies for access control
- Input validation on all endpoints
- SQL injection protection via SQLAlchemy ORM
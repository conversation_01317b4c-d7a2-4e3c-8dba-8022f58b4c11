"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2024-01-14

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Since tables are created by SQLAlchemy directly, we just need to ensure they exist
    # This migration serves as a baseline
    pass


def downgrade() -> None:
    # Drop all tables if needed
    pass
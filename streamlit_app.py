import streamlit as st
import asyncio
import os
import sys
import json
from typing import Dict, Any
import time

# Add agents to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

from agents.main import orchestrate_financial_query, FinancialDataResponse

# Set page config
st.set_page_config(
    page_title="Quantera AI",
    page_icon="💼",
    layout="wide"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .stAlert > div {
        padding: 0.5rem 1rem;
    }
    .confidence-high { color: #4caf50; font-weight: bold; }
    .confidence-medium { color: #ff9800; font-weight: bold; }
    .confidence-low { color: #f44336; font-weight: bold; }
    .citation-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #f9f9f9;
    }
    .citation-header {
        font-weight: bold;
        color: #333;
        margin-bottom: 0.5rem;
    }
    .citation-meta {
        font-size: 0.9em;
        color: #666;
        margin-bottom: 0.5rem;
    }
    .citation-preview {
        font-style: italic;
        color: #555;
        background-color: #f0f0f0;
        padding: 0.5rem;
        border-radius: 4px;
        margin-top: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

# Title and description
st.title("💼 Quantera AI")
st.markdown("Financial Research Agent.")

# Sidebar for configuration
st.sidebar.header("⚙️ Configuration")

# Database Configuration
st.sidebar.subheader("Database Settings")
user_id = st.sidebar.text_input("User ID", value="default_user")
collection_name = st.sidebar.text_input("Collection Name", value="documents")
qdrant_url = st.sidebar.text_input("Qdrant URL", value="http://localhost:6333")

# API Key status
st.sidebar.subheader("API Keys")
openai_key_status = "✅ Configured" if os.getenv("OPENAI_API_KEY") else "❌ Missing"
sec_key_status = "✅ Configured" if os.getenv("SEC_API_KEY") else "❌ Missing"
st.sidebar.markdown(f"**OpenAI API:** {openai_key_status}")
st.sidebar.markdown(f"**SEC API:** {sec_key_status}")

# Sample queries
st.sidebar.subheader("💡 Example Queries")
sample_queries = [
    "Analyze Apple's working capital trends from SEC filings",
    "What are the key financial metrics for Apple's quality of earnings?",
    "Compare Apple's revenue growth with their cash flow projections",
    "What SEC filings show Apple's latest financial disclosures?",
    "Analyze Apple's debt structure and repayment schedule"
]

selected_sample = st.sidebar.selectbox(
    "Choose a sample query:",
    [""] + sample_queries,
    index=0
)

# Main interface
st.header("💬 Financial Query Interface")

# Query input
user_query = st.text_area(
    "Enter your financial question:",
    value=selected_sample if selected_sample else "",
    placeholder="e.g., Analyze Apple's working capital trends from SEC filings and assess cash flow impact",
    height=100
)

# Search and generate button
if st.button("🚀 Analyze Financial Data", type="primary"):
    if not user_query.strip():
        st.warning("Please enter a financial question.")
    elif not os.getenv("OPENAI_API_KEY"):
        st.error("OpenAI API key is required. Please set OPENAI_API_KEY environment variable.")
    else:
        # Create containers for updates
        status_container = st.container()
        answer_container = st.container()
        
        with status_container:
            st.subheader("🔄 Analysis Progress")
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Step-by-step progress updates
            status_text.info("🤖 Initializing Financial Data Orchestrator...")
            progress_bar.progress(20)
            time.sleep(0.5)
            
            status_text.info("🧠 Analyzing query and determining optimal data sources...")
            progress_bar.progress(40)
            time.sleep(0.5)
            
            status_text.info("⚡ Executing financial analysis with AI agents...")
            progress_bar.progress(60)
            
            try:
                # Run the analysis
                response = asyncio.run(orchestrate_financial_query(
                    query=user_query,
                    user_id=user_id,
                    collection_name=collection_name,
                    qdrant_url=qdrant_url
                ))
                
                status_text.info("📊 Processing results and generating comprehensive answer...")
                progress_bar.progress(80)
                time.sleep(0.5)
                
                status_text.success("🎯 Analysis complete! Displaying results...")
                progress_bar.progress(100)
                time.sleep(0.5)
                
                # Display results
                with answer_container:
                    st.header("🎯 Financial Analysis Results")
                    
                    # Main answer with streaming effect
                    st.subheader("💬 Comprehensive Answer")
                    answer_placeholder = st.empty()
                    
                    # Simulate streaming the answer
                    full_answer = response.answer
                    streamed_text = ""
                    
                    for i in range(0, len(full_answer), 5):
                        streamed_text += full_answer[i:i+5]
                        answer_placeholder.markdown(streamed_text + "▊")
                        time.sleep(0.02)
                    
                    # Final answer without cursor
                    answer_placeholder.markdown(full_answer)
                    
                    # Create columns for metadata
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        # Source and methodology
                        st.subheader("🔍 Analysis Methodology")
                        source_badge = {
                            "qdrant": "🗄️ RAG Database",
                            "sec_api": "🏛️ SEC Filings", 
                            "mixed": "🔄 Combined Analysis",
                            "error": "❌ Error"
                        }
                        st.markdown(f"**Primary Source:** {source_badge.get(response.source, response.source)}")
                        if response.summary:
                            st.markdown(f"**Summary:** {response.summary}")
                    
                    with col2:
                        # Confidence score (for RAG responses)
                        if hasattr(response, 'confidence_score') and response.confidence_score > 0:
                            st.subheader("📊 Confidence Score")
                            confidence = response.confidence_score
                            
                            if confidence >= 0.8:
                                conf_class = "confidence-high"
                                conf_emoji = "🟢"
                            elif confidence >= 0.6:
                                conf_class = "confidence-medium"
                                conf_emoji = "🟡"
                            else:
                                conf_class = "confidence-low"
                                conf_emoji = "🔴"
                            
                            st.markdown(
                                f'<div class="{conf_class}">{conf_emoji} {confidence:.1%}</div>',
                                unsafe_allow_html=True
                            )
                            st.progress(confidence)
                    
                    # Enhanced Citations Section
                    st.subheader("📚 Citations & Sources")
                    
                    citations_found = False
                    
                    # RAG Citations
                    if hasattr(response, 'rag_citations') and response.rag_citations:
                        citations_found = True
                        st.markdown("#### 📄 RAG Database Citations")
                        for citation in response.rag_citations:
                            st.markdown(
                                f'''
                                <div class="citation-card">
                                    <div class="citation-header">
                                        📚 {citation.get('citation_id', 'N/A')} - {citation.get('source_type', 'RAG Database')}
                                    </div>
                                    <div class="citation-meta">
                                        <strong>Document ID:</strong> {citation.get('document_id', 'N/A')}<br>
                                        <strong>Relevance Score:</strong> {citation.get('relevance_score', 0):.3f}<br>
                                        <strong>Chunk:</strong> #{citation.get('chunk_index', 'N/A')}
                                    </div>
                                    <div class="citation-preview">
                                        <strong>Content Preview:</strong><br>
                                        {citation.get('content_preview', 'No preview available')}
                                    </div>
                                </div>
                                ''',
                                unsafe_allow_html=True
                            )
                    
                    # Check for legacy RAG data (fallback)
                    elif hasattr(response, 'used_doc_ids') and response.used_doc_ids:
                        citations_found = True
                        st.markdown("#### 📄 RAG Database Sources")
                        for i, doc_id in enumerate(response.used_doc_ids, 1):
                            st.markdown(f"**{i}.** Document ID: `{doc_id}`")
                    
                    # SEC Citations
                    if hasattr(response, 'sec_citations') and response.sec_citations:
                        citations_found = True
                        st.markdown("#### 🏛️ SEC Filing Citations")
                        for citation in response.sec_citations:
                            st.markdown(
                                f'''
                                <div class="citation-card">
                                    <div class="citation-header">
                                        🏛️ {citation.get('citation_id', 'N/A')} - {citation.get('document_type', 'SEC Filing')}
                                    </div>
                                    <div class="citation-meta">
                                        <strong>Company:</strong> {citation.get('company_name', 'N/A')} ({citation.get('ticker', 'N/A')})<br>
                                        <strong>Filed:</strong> {citation.get('filed_date', 'N/A')}<br>
                                        <strong>Accession:</strong> {citation.get('accession_number', 'N/A')}<br>
                                        {f"<strong>Period End:</strong> {citation.get('period_end_date', 'N/A')}<br>" if citation.get('period_end_date') else ""}
                                    </div>
                                    {f'<div class="citation-preview"><strong>Description:</strong><br>{citation.get("description", "No description available")}</div>' if citation.get('description') else ''}
                                    {f'<div style="margin-top: 0.5rem;"><a href="{citation.get("filing_url", "#")}" target="_blank">🔗 View SEC Filing</a></div>' if citation.get('filing_url') else ''}
                                </div>
                                ''',
                                unsafe_allow_html=True
                            )
                    
                    # Check for legacy SEC data (fallback)
                    elif hasattr(response, 'sec_sources') and response.sec_sources:
                        citations_found = True
                        st.markdown("#### 🏛️ SEC Filing Sources")
                        for i, source in enumerate(response.sec_sources, 1):
                            st.markdown(f"**{i}. {source.get('title', 'Unknown Filing')}**")
                            st.markdown(f"   📅 Filed: {source.get('filed_date', 'Unknown')}")
                            if source.get('filing_url'):
                                st.markdown(f"   🔗 [View Filing]({source['filing_url']})")
                            st.markdown("---")
                    
                    # If no citations found, show debug info
                    if not citations_found:
                        st.warning("No citations were generated for this analysis.")
                        with st.expander("🔧 Debug Information"):
                            st.markdown("**Response attributes:**")
                            st.json({
                                "source": response.source,
                                "has_rag_citations": hasattr(response, 'rag_citations'),
                                "rag_citations_count": len(getattr(response, 'rag_citations', [])),
                                "has_sec_citations": hasattr(response, 'sec_citations'), 
                                "sec_citations_count": len(getattr(response, 'sec_citations', [])),
                                "has_used_doc_ids": hasattr(response, 'used_doc_ids'),
                                "used_doc_ids_count": len(getattr(response, 'used_doc_ids', [])),
                                "has_sec_sources": hasattr(response, 'sec_sources'),
                                "sec_sources_count": len(getattr(response, 'sec_sources', []))
                            })
                    
                    # Raw data section
                    if hasattr(response, 'data') and response.data:
                        with st.expander(f"📈 Raw Data ({len(response.data)} items)"):
                            st.json(response.data[:3])
                            if len(response.data) > 3:
                                st.info(f"... and {len(response.data) - 3} more items")
                
            except Exception as e:
                status_text.error(f"❌ Error during analysis: {str(e)}")
                progress_bar.progress(0)
                st.exception(e)

# Debug information
if st.sidebar.checkbox("🔧 Show Debug Info"):
    st.sidebar.subheader("Debug Information")
    st.sidebar.json({
        "User ID": user_id,
        "Collection": collection_name,
        "Qdrant URL": qdrant_url,
        "OpenAI Key": bool(os.getenv("OPENAI_API_KEY")),
        "SEC API Key": bool(os.getenv("SEC_API_KEY"))
    }) 
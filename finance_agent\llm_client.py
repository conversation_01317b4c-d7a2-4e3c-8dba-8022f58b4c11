import os
import json
import time
from dotenv import load_dotenv
import openai

# Load API keys from .env
load_dotenv()
agentic_api_key = os.getenv("AGENTIC_API_KEY")
agentic_base_url = os.getenv("AGENTIC_BASE_URL")

EXTRACTION_PROMPT = """
You are an expert financial analyst with strong storytelling capabilities. You will receive a page from a financial document and a specific query.

Your task is to:
1. Analyze the provided page content for information relevant to the query
2. Extract and present ONLY the facts and figures that directly answer the query
3. Format your response in clean markdown
4. Focus strictly on quantitative data, specific dates, percentages, dollar amounts, and concrete facts
5. **ADDITIONALLY**: Extract relevant context, trends, and business implications that support financial storytelling
6. If the page does not contain sufficient information to answer the query, respond with exactly "Not Found"
7. IMPORTANT: You are analyzing Page {page_number} of the document - keep this context in mind for accuracy

Guidelines:
- **AGGRESSIVE FACT-CHECKING**: Be extraordinarily careful with ALL facts, numbers, percentages, dates, and monetary values - double-check every figure
- **ZERO TOLERANCE FOR INACCURACY**: Never approximate, round, or estimate numbers - use EXACT figures as stated in the document
- **EXPLICIT MENTION ONLY**: Only include information that is explicitly and clearly stated in the document - no assumptions or inferences
- Be precise and factual - no speculation or general statements
- Include specific numbers, percentages, dates, and monetary values when available
- **Extract contextual information** that explains the "why" behind the numbers (business reasons, market conditions, strategic decisions)
- **Identify trends and patterns** mentioned in the text (growth, decline, seasonal patterns, etc.)
- **Capture business implications** and strategic context when explicitly stated
- Use clear markdown formatting (headers, bullet points, tables as appropriate)
- Only include information that directly addresses the query
- If partial information is available, present only what is clearly stated in the document
- Remember you are analyzing Page {page_number} - this may help provide context for continuity of information
- **Structure your response** to separate hard data from contextual insights for better narrative flow

Query: {query}

Page Number: {page_number}

Page Content:
"""

def log_page_result(page_number, query, result, processing_time):
    """Log detailed information about each page's processing result"""
    print(f"\n{'='*60}")
    print(f"📄 PAGE {page_number} PROCESSING COMPLETE")
    print(f"{'='*60}")
    print(f"🔍 Query: {query}")
    print(f"⏱️  Processing Time: {processing_time:.2f}s")
    
    if result == "Not Found":
        print(f"❌ Result: No relevant information found on this page")
    else:
        print(f"✅ Result: Information extracted successfully")
        print(f"📊 Content Length: {len(result)} characters")
        
        # Show a preview of the result (first 200 characters)
        preview = result[:200] + "..." if len(result) > 200 else result
        print(f"📝 Preview:")
        print(f"   {preview}")
    
    print(f"{'='*60}\n")

async def extract_fields_from_page(page_text, query, page_number):
    start_time = time.time()
    task_id = id(page_text) % 1000  # Simple task identifier
    print(f"🚀 [Task {task_id}] Starting processing page {page_number} with {len(page_text)} characters for query: '{query}'")
    
    # Configure OpenAI client for agentic with connection pooling
    client = openai.AsyncOpenAI(
        api_key=agentic_api_key,
        base_url=agentic_base_url,
        max_retries=3,
        timeout=60.0
    )

    prompt = EXTRACTION_PROMPT.format(query=query, page_number=page_number)
    context = page_text
    print(f"📡 [Task {task_id}] Calling agentic model for page {page_number}...")
    try:
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": f"You are an expert financial analyst with storytelling capabilities. Answer queries about financial documents with precise facts, figures, and contextual insights in markdown format. You are currently analyzing Page {page_number}."},
                {"role": "user", "content": prompt + context}
            ],
            max_tokens=4000,
            temperature=0.3  # Lower temperature for more factual responses
        )
        result = response.choices[0].message.content.strip()
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Log detailed result for this page
        log_page_result(page_number, query, result, processing_time)
        
        return result
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        error_msg = f"Error calling agentic model for page {page_number}: {e}"
        print(f"❌ [Task {task_id}] {error_msg}")
        
        # Log error result
        log_page_result(page_number, query, "Not Found", processing_time)
        
        return "Not Found" 
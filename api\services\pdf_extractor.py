import io
import time
from typing import Optional, Dict, Any
from pathlib import Path
import hashlib
import json
import boto3
from botocore.exceptions import ClientError

try:
    import PyPDF2
    HAS_PYPDF2 = True
except ImportError:
    HAS_PYPDF2 = False

try:
    import pdfplumber
    HAS_PDFPLUMBER = True
except ImportError:
    HAS_PDFPLUMBER = False

from api.services.storage import storage_service
from api.utils.logger import logger


class PDFExtractor:
    """Service for extracting text content from PDFs stored in S3"""
    
    def __init__(self):
        self.cache_dir = Path(".cache/pdf_extracts")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Check for PDF processing libraries
        if not HAS_PYPDF2 and not HAS_PDFPLUMBER:
            raise ImportError("Either PyPDF2 or pdfplumber must be installed for PDF processing")
        
        self.preferred_library = "pdfplumber" if HAS_PDFPLUMBER else "pypdf2"
        logger.info(f"PDFExtractor initialized with {self.preferred_library} library")
    
    def _get_cache_key(self, bucket_key: str, page_number: int = 1) -> str:
        """Generate cache key for a PDF page"""
        key_hash = hashlib.md5(f"{bucket_key}:page:{page_number}".encode()).hexdigest()
        return f"{key_hash}.json"
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get full path for cache file"""
        return self.cache_dir / cache_key
    
    def _load_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Load extracted content from cache"""
        cache_path = self._get_cache_path(cache_key)
        
        if not cache_path.exists():
            return None
        
        try:
            with open(cache_path, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                
            # Check if cache is still valid (24 hours)
            cache_age = time.time() - cached_data.get('timestamp', 0)
            if cache_age > 86400:  # 24 hours
                logger.debug(f"Cache expired for {cache_key}")
                cache_path.unlink()  # Remove expired cache
                return None
            
            logger.debug(f"Cache hit for {cache_key}")
            return cached_data
            
        except (json.JSONDecodeError, KeyError, FileNotFoundError) as e:
            logger.warning(f"Invalid cache file {cache_key}: {str(e)}")
            try:
                cache_path.unlink()
            except FileNotFoundError:
                pass
            return None
    
    def _save_to_cache(self, cache_key: str, content: str, metadata: Dict[str, Any]) -> None:
        """Save extracted content to cache"""
        cache_path = self._get_cache_path(cache_key)
        
        cache_data = {
            'content': content,
            'metadata': metadata,
            'timestamp': time.time()
        }
        
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            logger.debug(f"Cached extraction for {cache_key}")
        except Exception as e:
            logger.warning(f"Failed to cache extraction {cache_key}: {str(e)}")
    
    def _download_pdf_from_s3(self, bucket_key: str) -> Optional[bytes]:
        """Download PDF content from S3"""
        try:
            logger.debug(f"Downloading PDF from S3: {bucket_key}")
            
            response = storage_service.client.get_object(
                Bucket=storage_service.bucket_name,
                Key=bucket_key
            )
            
            pdf_content = response['Body'].read()
            logger.debug(f"Downloaded {len(pdf_content)} bytes from {bucket_key}")
            return pdf_content
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchKey':
                logger.error(f"PDF not found in S3: {bucket_key}")
            else:
                logger.error(f"S3 error downloading {bucket_key}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading {bucket_key}: {str(e)}")
            return None
    
    def _extract_text_with_pypdf2(self, pdf_content: bytes, page_number: int = 1) -> Optional[str]:
        """Extract text using PyPDF2"""
        try:
            pdf_file = io.BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            if len(pdf_reader.pages) == 0:
                logger.warning("PDF has no pages")
                return None
            
            if page_number > len(pdf_reader.pages):
                logger.warning(f"Page {page_number} does not exist (PDF has {len(pdf_reader.pages)} pages)")
                return None
            
            page = pdf_reader.pages[page_number - 1]  # Convert to 0-based index
            text = page.extract_text()
            
            return text.strip() if text else None
            
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {str(e)}")
            return None
    
    def _extract_text_with_pdfplumber(self, pdf_content: bytes, page_number: int = 1) -> Optional[str]:
        """Extract text using pdfplumber"""
        try:
            pdf_file = io.BytesIO(pdf_content)
            
            with pdfplumber.open(pdf_file) as pdf:
                if len(pdf.pages) == 0:
                    logger.warning("PDF has no pages")
                    return None
                
                if page_number > len(pdf.pages):
                    logger.warning(f"Page {page_number} does not exist (PDF has {len(pdf.pages)} pages)")
                    return None
                
                page = pdf.pages[page_number - 1]  # Convert to 0-based index
                text = page.extract_text()
                
                return text.strip() if text else None
                
        except Exception as e:
            logger.error(f"pdfplumber extraction failed: {str(e)}")
            return None
    
    def extract_first_page_text(self, bucket_key: str) -> Optional[str]:
        """Extract text from the first page of a PDF"""
        start_time = time.time()
        
        # Check cache first
        cache_key = self._get_cache_key(bucket_key, page_number=1)
        cached_data = self._load_from_cache(cache_key)
        
        if cached_data:
            logger.info(f"Using cached text for {bucket_key} (length: {len(cached_data['content'])})")
            return cached_data['content']
        
        # Download PDF from S3
        pdf_content = self._download_pdf_from_s3(bucket_key)
        if not pdf_content:
            return None
        
        # Extract text
        text = None
        extraction_method = None
        
        if self.preferred_library == "pdfplumber" and HAS_PDFPLUMBER:
            text = self._extract_text_with_pdfplumber(pdf_content, page_number=1)
            extraction_method = "pdfplumber"
            
            # Fallback to PyPDF2 if pdfplumber fails
            if not text and HAS_PYPDF2:
                logger.info("pdfplumber failed, falling back to PyPDF2")
                text = self._extract_text_with_pypdf2(pdf_content, page_number=1)
                extraction_method = "pypdf2_fallback"
        
        elif HAS_PYPDF2:
            text = self._extract_text_with_pypdf2(pdf_content, page_number=1)
            extraction_method = "pypdf2"
        
        # Process extraction results
        if text:
            processing_time = time.time() - start_time
            metadata = {
                'bucket_key': bucket_key,
                'page_number': 1,
                'extraction_method': extraction_method,
                'processing_time': processing_time,
                'text_length': len(text),
                'pdf_size': len(pdf_content)
            }
            
            # Cache the result
            self._save_to_cache(cache_key, text, metadata)
            
            logger.info(f"Extracted {len(text)} characters from {bucket_key} "
                       f"(method: {extraction_method}, time: {processing_time:.2f}s)")
            return text
        else:
            logger.warning(f"Failed to extract text from {bucket_key}")
            return None
    
    def extract_page_text(self, bucket_key: str, page_number: int) -> Optional[str]:
        """Extract text from a specific page of a PDF"""
        start_time = time.time()
        
        # Check cache first
        cache_key = self._get_cache_key(bucket_key, page_number=page_number)
        cached_data = self._load_from_cache(cache_key)
        
        if cached_data:
            logger.info(f"Using cached text for {bucket_key} page {page_number}")
            return cached_data['content']
        
        # Download PDF from S3
        pdf_content = self._download_pdf_from_s3(bucket_key)
        if not pdf_content:
            return None
        
        # Extract text
        text = None
        extraction_method = None
        
        if self.preferred_library == "pdfplumber" and HAS_PDFPLUMBER:
            text = self._extract_text_with_pdfplumber(pdf_content, page_number=page_number)
            extraction_method = "pdfplumber"
            
            # Fallback to PyPDF2 if pdfplumber fails
            if not text and HAS_PYPDF2:
                text = self._extract_text_with_pypdf2(pdf_content, page_number=page_number)
                extraction_method = "pypdf2_fallback"
        
        elif HAS_PYPDF2:
            text = self._extract_text_with_pypdf2(pdf_content, page_number=page_number)
            extraction_method = "pypdf2"
        
        # Process extraction results
        if text:
            processing_time = time.time() - start_time
            metadata = {
                'bucket_key': bucket_key,
                'page_number': page_number,
                'extraction_method': extraction_method,
                'processing_time': processing_time,
                'text_length': len(text),
                'pdf_size': len(pdf_content)
            }
            
            # Cache the result
            self._save_to_cache(cache_key, text, metadata)
            
            logger.info(f"Extracted {len(text)} characters from {bucket_key} page {page_number} "
                       f"(method: {extraction_method}, time: {processing_time:.2f}s)")
            return text
        else:
            logger.warning(f"Failed to extract text from {bucket_key} page {page_number}")
            return None
    
    def clear_cache(self) -> Dict[str, Any]:
        """Clear all cached PDF extractions"""
        cache_files = list(self.cache_dir.glob("*.json"))
        removed_count = 0
        
        for cache_file in cache_files:
            try:
                cache_file.unlink()
                removed_count += 1
            except Exception as e:
                logger.warning(f"Failed to remove cache file {cache_file}: {str(e)}")
        
        result = {
            "cleared": removed_count,
            "message": f"Cleared {removed_count} cached PDF extractions"
        }
        
        logger.info(f"Cleared PDF extraction cache: {result}")
        return result
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the PDF extraction cache"""
        cache_files = list(self.cache_dir.glob("*.json"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        stats = {
            "total_cached_files": len(cache_files),
            "total_cache_size_bytes": total_size,
            "total_cache_size_mb": total_size / (1024 * 1024),
            "cache_directory": str(self.cache_dir)
        }
        
        return stats


# Singleton instance
pdf_extractor = PDFExtractor()
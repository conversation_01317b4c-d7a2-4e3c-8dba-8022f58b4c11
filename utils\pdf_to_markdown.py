import os
import base64
import json
import time
from pathlib import Path
import requests
from dotenv import load_dotenv
import asyncio
from enum import Enum
from typing import List, Optional

# New imports for additional parsers
try:
    import pymupdf4llm
    PYMUPDF4LLM_AVAILABLE = True
except ImportError:
    PYMUPDF4LLM_AVAILABLE = False
    print("Warning: pymupdf4llm not available. Install it with: pip install pymupdf4llm")

try:
    from llama_cloud_services import LlamaParse
    LLAMAPARSE_AVAILABLE = True
except ImportError:
    LLAMAPARSE_AVAILABLE = False
    print("Warning: llama_cloud_services not available. Install it with: pip install llama-cloud-services")

class ParserType(Enum):
    """Enum for different PDF parser types"""
    JINA = "jina"
    PYMUPDF4LLM = "pymupdf4llm"  
    LLAMAPARSE = "llamaparse"

def convert_pdf_to_markdown_pymupdf4llm(pdf_path):
    """
    Convert a PDF file to markdown using pymupdf4llm
    
    Args:
        pdf_path (str): Path to the PDF file
    
    Returns:
        str: Markdown content
    """
    if not PYMUPDF4LLM_AVAILABLE:
        print(f"pymupdf4llm not available. Cannot convert {pdf_path}")
        return None
    
    try:
        print(f"Converting {pdf_path} using pymupdf4llm...")
        
        # Convert PDF to markdown using pymupdf4llm with page chunks
        page_chunks = pymupdf4llm.to_markdown(pdf_path, page_chunks=True)
        
        if page_chunks and isinstance(page_chunks, list):
            # Extract text from each page chunk and join with separators
            markdown_parts = []
            for chunk in page_chunks:
                if isinstance(chunk, dict) and 'text' in chunk:
                    text = chunk['text'].strip()
                    if text:  # Only add non-empty text
                        markdown_parts.append(text)
            
            # Join pages with same separator as LlamaParse
            markdown_content = '\n\n================================================================================\n\n'.join(markdown_parts)
            
            print(f"✓ Successfully converted {pdf_path} using pymupdf4llm ({len(markdown_parts)} pages)")
            return markdown_content
        else:
            print(f"✗ Failed to convert {pdf_path} using pymupdf4llm - no page chunks returned")
            return None
            
    except Exception as e:
        print(f"Exception occurred while converting {pdf_path} with pymupdf4llm: {str(e)}")
        return None

async def convert_pdf_to_markdown_llamaparse(pdf_path, api_key=None):
    """
    Convert a PDF file to markdown using LlamaParse
    
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str, optional): LlamaParse API key
    
    Returns:
        str: Markdown content
    """
    if not LLAMAPARSE_AVAILABLE:
        print(f"llama_cloud_services not available. Cannot convert {pdf_path}")
        return None
    
    try:
        print(f"Converting {pdf_path} using LlamaParse...")
        
        # Use API key from parameter or environment
        llamaparse_api_key = api_key or os.getenv("LLAMAPARSE_API_KEY")
        
        if not llamaparse_api_key:
            print("LlamaParse API key not found. Set LLAMAPARSE_API_KEY environment variable.")
            return None
        
        # Initialize LlamaParse
        parser = LlamaParse(
            api_key=llamaparse_api_key,
            num_workers=4,
            verbose=True,
            language="en",
        )
        
        # Parse the PDF
        result = await parser.aparse(pdf_path)
        
        if result:
            # Extract markdown content from the result
            markdown_parts = []
            
            # Handle the result - it might be a tuple or list
            if isinstance(result, tuple):
                # If it's a tuple, extract the actual results
                parse_results = result[0] if result else []
            else:
                parse_results = result
            
            # Make sure we have a list to iterate over
            if not isinstance(parse_results, list):
                parse_results = [parse_results]
            
            for job_result in parse_results:
                if hasattr(job_result, 'pages'):
                    for page in job_result.pages:
                        if hasattr(page, 'md'):
                            markdown_parts.append(page.md)
                        elif hasattr(page, 'text'):
                            # Fallback to text if md is not available
                            markdown_parts.append(page.text)
            
            markdown_content = '\n\n================================================================================\n\n'.join(markdown_parts)
            
            if markdown_content:
                print(f"✓ Successfully converted {pdf_path} using LlamaParse")
                return markdown_content
            else:
                print(f"✗ Failed to convert {pdf_path} using LlamaParse - no content returned")
                return None
        else:
            print(f"✗ Failed to convert {pdf_path} using LlamaParse - no result returned")
            return None
            
    except Exception as e:
        print(f"Exception occurred while converting {pdf_path} with LlamaParse: {str(e)}")
        return None


def convert_pdf_to_markdown_jina(pdf_path, api_key=None):
    """
    Convert a PDF file to markdown using Jina AI Reader API
    Process each page individually to ensure proper page separation
    
    Args:
        pdf_path (str): Path to the PDF file
        api_key (str, optional): Jina AI API key for higher rate limits
    
    Returns:
        str: Markdown content
    """
    
    try:
        print(f"Converting {pdf_path} using Jina AI Reader...")
        
        # Import PyMuPDF for page-by-page processing
        try:
            import pymupdf
        except ImportError:
            print("PyMuPDF not available. Cannot process pages individually.")
            return None
        
        # Open the PDF to get page count and process each page
        doc = pymupdf.open(pdf_path)
        page_count = len(doc)
        
        if page_count == 0:
            print(f"✗ No pages found in {pdf_path}")
            return None
        
        print(f"Processing {page_count} pages individually...")
        
        # Set headers for Jina API
        headers = {
            'Content-Type': 'application/json',
        }
        
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
        
        page_contents = []
        
        # Process each page individually
        for page_num in range(page_count):
            try:
                print(f"Processing page {page_num + 1}/{page_count}...")
                
                # Create a new PDF with just this page
                single_page_doc = pymupdf.open()
                single_page_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
                
                # Convert single page to bytes
                single_page_bytes = single_page_doc.write()
                single_page_doc.close()
                
                # Encode to base64
                single_page_base64 = base64.b64encode(single_page_bytes).decode('utf-8')
                
                # Prepare the request payload
                payload = {
                    "url": None, 
                    "pdf": single_page_base64,
                    "filename": f"{os.path.basename(pdf_path)}_page_{page_num + 1}.pdf"
                }
                
                # Make the API request
                response = requests.post(
                    "https://r.jina.ai/",
                    json=payload,
                    headers=headers,
                    timeout=120  
                )
                
                if response.status_code == 200:
                    content = response.text.strip()
                    
                    # Check if we got a valid response
                    if content and "Access denied" not in content and "Cloudflare" not in content:
                        page_contents.append(content)
                    else:
                        print(f"✗ Failed to process page {page_num + 1}")
                        page_contents.append(f"[Page {page_num + 1} could not be processed]")
                else:
                    print(f"✗ Error processing page {page_num + 1}: {response.status_code}")
                    page_contents.append(f"[Page {page_num + 1} could not be processed]")
                
                # Small delay to avoid rate limiting
                time.sleep(1)
                
            except Exception as e:
                print(f"✗ Exception processing page {page_num + 1}: {str(e)}")
                page_contents.append(f"[Page {page_num + 1} could not be processed]")
        
        doc.close()
        
        if page_contents:
            # Join all pages with the same separator as LlamaParse
            markdown_content = '\n\n================================================================================\n\n'.join(page_contents)
            print(f"✓ Successfully converted {pdf_path} using Jina AI Reader ({len(page_contents)} pages)")
            return markdown_content
        else:
            print(f"✗ Failed to convert any pages from {pdf_path}")
            return None
            
    except Exception as e:
        print(f"Exception occurred while converting {pdf_path} with Jina AI Reader: {str(e)}")
        return None

async def convert_all_pdfs_in_directory(data_dir="data", output_dir="output", api_key=None, parser: List[ParserType] = None):
    """
    Convert all PDF files in the data directory to markdown
    
    Args:
        data_dir (str): Directory containing PDF files
        output_dir (str): Directory to save markdown files
        api_key (str, optional): Jina AI API key
        parser (List[ParserType], optional): List of parsers to use. Defaults to [ParserType.JINA]
    """
    
    # Set default parsers if none provided
    if parser is None:
        parser = [ParserType.JINA]
    
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(exist_ok=True)
    
    # Find all PDF files in data directory
    pdf_files = list(Path(data_dir).glob("*.pdf"))
    
    if not pdf_files:
        print(f"No PDF files found in {data_dir}/")
        return
    
    print(f"Found {len(pdf_files)} PDF file(s) to convert:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")
    
    print(f"Using parsers: {[p.value for p in parser]}")
    
    successful_conversions = []
    
    # Convert each PDF file
    for pdf_file in pdf_files:
        markdown_content = None
        
        # Try each parser in order until one succeeds
        for current_parser in parser:
            if current_parser == ParserType.JINA:
                markdown_content = convert_pdf_to_markdown_jina(str(pdf_file), api_key)
            elif current_parser == ParserType.PYMUPDF4LLM:
                markdown_content = convert_pdf_to_markdown_pymupdf4llm(str(pdf_file))
            elif current_parser == ParserType.LLAMAPARSE:
                markdown_content = await convert_pdf_to_markdown_llamaparse(str(pdf_file), api_key)
            
            # If we got valid content, break out of parser loop
            if markdown_content and len(markdown_content) > 50:
                break
        
        if markdown_content and len(markdown_content) > 50:
            # Create output filename
            output_filename = pdf_file.stem + ".md"
            output_path = Path(output_dir) / output_filename
            
            # Save markdown content
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            print(f"✓ Converted {pdf_file.name} -> {output_path}")
            print(f"  Content length: {len(markdown_content)} characters")
            print(f"  Preview: {markdown_content[:200]}...")
            successful_conversions.append(output_path)
        else:
            print(f"✗ Failed to convert {pdf_file.name}")
        
        # Wait between requests to avoid rate limiting
        time.sleep(3)

if __name__ == "__main__":
    load_dotenv()
    
    api_key = os.getenv("JINA_API_KEY")
    llamaparse_api_key = os.getenv("LLAMAPARSE_API_KEY")
    
    if not api_key:
        print("Warning: JINA_API_KEY not found in environment variables.")
        print("You can set it in a .env file or as an environment variable.")
        print("Continuing without API key (may have rate limits)...")
        api_key = None
    
    if not llamaparse_api_key:
        print("Warning: LLAMAPARSE_API_KEY not found in environment variables.")
        print("You can set it in a .env file or as an environment variable.")
        print("LlamaParse will not be available...")
    
    print("Starting PDF to Markdown conversion...")
    
    # You can specify which parsers to use:
    # Option 1: Use only JINA (default)
    # asyncio.run(convert_all_pdfs_in_directory(api_key=api_key)) 
    # Option 2: Use only pymupdf4llm
    # convert_all_pdfs_in_directory(api_key=None, parser=[ParserType.PYMUPDF4LLM])
    
    # Option 3: Use only LlamaParse
    asyncio.run(convert_all_pdfs_in_directory(api_key=llamaparse_api_key, parser=[ParserType.LLAMAPARSE]))
    
    # Option 4: Try multiple parsers in order (fallback)
    # convert_all_pdfs_in_directory(api_key=api_key, parser=[ParserType.JINA, ParserType.PYMUPDF4LLM, ParserType.LLAMAPARSE])
    
    print("Conversion complete!") 

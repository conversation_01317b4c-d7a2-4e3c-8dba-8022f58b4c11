import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    # DigitalOcean Spaces
    do_spaces_endpoint: str = Field(default="https://quanteraai.sfo3.digitaloceanspaces.com/")
    do_spaces_access_key: str = Field(default="")
    do_spaces_secret_key: str = Field(default="")
    do_spaces_bucket: str = Field(default="quanteraai")
    do_spaces_region: str = Field(default="sfo3")
    
    # Database
    database_url: str = Field(default="sqlite:///./upload_sessions.db")
    database_pool_size: int = Field(default=20)
    database_max_overflow: int = Field(default=40)
    database_pool_timeout: int = Field(default=30)
    
    # API Configuration
    api_prefix: str = Field(default="/api/v1")
    log_level: str = Field(default="INFO")
    debug: bool = Field(default=False)
    
    # Upload Configuration
    max_upload_size: int = Field(default=5368709120)  # 5GB default
    presigned_url_expiry: int = Field(default=900)  # 15 minutes
    
    # CORS
    cors_origins: str = Field(default="*")
    
    # Environment
    environment: str = Field(default="production")
    dev_auto_upload: bool = Field(default=False)
    dev_auto_process: bool = Field(default=False)
    
    def get_cors_origins(self) -> List[str]:
        """Get CORS origins as a list"""
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"
    )


settings = Settings()
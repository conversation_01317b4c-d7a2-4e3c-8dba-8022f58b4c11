import os
import sys
import json
import hashlib
import numpy as np
from typing import List, Tuple, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from sklearn.cluster import KMeans
from dotenv import load_dotenv
from openai import OpenAI

# Add the parent directory to the path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.pdf_to_markdown import convert_pdf_to_markdown_jina

# Load environment variables
load_dotenv()

# Global OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
jina_api_key = os.getenv("JINA_API_KEY")

# Cache directory for markdown files
CACHE_DIR = "summary_agent/cache"
RESULTS_DIR = "summary_agent/results"

# Ensure directories exist
os.makedirs(CACHE_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)


def get_file_hash(file_path: str) -> str:
    """Generate hash for a file to use as cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()


def get_cached_markdown(pdf_path: str) -> str:
    """Get cached markdown content if available."""
    try:
        file_hash = get_file_hash(pdf_path)
        cache_file = os.path.join(CACHE_DIR, f"{file_hash}.md")
        
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():  # Only return if non-empty
                    print(f"Using cached markdown for {os.path.basename(pdf_path)}")
                    return content
    except Exception as e:
        print(f"Error reading cache for {pdf_path}: {e}")
    
    return None


def cache_markdown(pdf_path: str, markdown_content: str) -> None:
    """Cache markdown content for future use."""
    try:
        file_hash = get_file_hash(pdf_path)
        cache_file = os.path.join(CACHE_DIR, f"{file_hash}.md")
        
        with open(cache_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"Cached markdown for {os.path.basename(pdf_path)}")
    except Exception as e:
        print(f"Error caching markdown for {pdf_path}: {e}")


def read_pdf_with_jina(pdf_path: str) -> str:
    """Read PDF content using Jina AI with caching."""
    # Check cache first
    cached_content = get_cached_markdown(pdf_path)
    if cached_content:
        return cached_content
    
    # Read from PDF using Jina AI
    try:
        print(f"Reading PDF: {pdf_path}")
        markdown_content = convert_pdf_to_markdown_jina(pdf_path, jina_api_key)
        
        if not markdown_content:
            raise ValueError("Failed to extract content from PDF using Jina AI")
        
        print(f"Successfully extracted {len(markdown_content)} characters from PDF")
        
        # Cache the result
        cache_markdown(pdf_path, markdown_content)
        
        return markdown_content
    
    except Exception as e:
        print(f"Error reading PDF with Jina AI: {e}")
        raise


def chunk_text_by_pages(text: str) -> List[str]:
    """Split text into chunks based on pages."""
    page_separator = '\n\n================================================================================\n\n'
    pages = text.split(page_separator)
    
    page_chunks = []
    for i, page in enumerate(pages):
        cleaned_page = page.strip()
        if cleaned_page:
            page_chunks.append(f"Page {i+1}:\n{cleaned_page}")
    
    return page_chunks


def chunk_text_by_characters(text: str, chunk_size: int = 1000) -> List[str]:
    """Split text into chunks based on character count."""
    words = text.split()
    chunks = []
    
    for i in range(0, len(words), chunk_size):
        chunk = " ".join(words[i:i + chunk_size])
        chunks.append(f"Chunk {len(chunks)+1}:\n{chunk}")
    
    return chunks


def get_embeddings_batch(chunks: List[str], embedding_model: str = "text-embedding-3-small") -> np.ndarray:
    """Get embeddings for a list of chunks."""
    embeddings_list = []
    
    for i, chunk in enumerate(chunks):
        try:
            embedding_response = client.embeddings.create(
                input=chunk,
                model=embedding_model
            )
            embeddings_list.append(embedding_response.data[0].embedding)
            
        except Exception as e:
            print(f"Error getting embedding for chunk {i+1}: {e}")
            if embeddings_list:
                embeddings_list.append(np.zeros_like(embeddings_list[0]))
            else:
                continue
    
    return np.array(embeddings_list)


def cluster_document(embeddings: np.ndarray, num_clusters: int = 10) -> Tuple[np.ndarray, np.ndarray]:
    """Cluster document embeddings using KMeans."""
    if len(embeddings) < num_clusters:
        num_clusters = max(1, len(embeddings) // 2)
    
    kmeans = KMeans(n_clusters=num_clusters, random_state=0, n_init="auto")
    labels = kmeans.fit_predict(embeddings)
    
    return labels, kmeans.cluster_centers_


def find_representative_chunks(chunks: List[str], embeddings: np.ndarray,
                              labels: np.ndarray, centers: np.ndarray) -> List[str]:
    """Find the most representative chunk for each cluster."""
    representative_chunks = []
    unique_labels = np.unique(labels)
    
    for label in unique_labels:
        mask = labels == label
        cluster_embeddings = embeddings[mask]
        cluster_chunks = np.array(chunks)[mask]
        
        if len(cluster_chunks) > 0:
            distances = np.linalg.norm(cluster_embeddings - centers[label], axis=1)
            representative_chunk = cluster_chunks[np.argmin(distances)]
            representative_chunks.append(representative_chunk)
    
    return representative_chunks


def generate_summary(text: str, num_clusters: int = 10, use_page_based: bool = True, 
                    chunk_size: int = 1000, agentic_model: str = "gpt-4o-mini") -> str:
    """Generate summary using clustering approach."""
    method = "page-based" if use_page_based else "character-based"
    chunk_type = "pages" if use_page_based else "chunks"
    
    print(f"Starting {method} summarization process...")
    
    # Step 1: Extract chunks
    if use_page_based:
        chunks = chunk_text_by_pages(text)
    else:
        chunks = chunk_text_by_characters(text, chunk_size)
    
    if not chunks:
        return "No content to summarize."
    
    print(f"Processing {len(chunks)} {chunk_type}")
    
    # Step 2: Get embeddings
    embeddings = get_embeddings_batch(chunks)
    if len(embeddings) == 0:
        return "Failed to generate embeddings."
    
    print(f"{chunk_type.capitalize()} embeddings generated successfully")
    
    # Step 3: Cluster embeddings
    labels, centers = cluster_document(embeddings, num_clusters)
    print(f"{chunk_type.capitalize()} clustering completed with {len(np.unique(labels))} clusters")
    
    # Step 4: Find representative chunks
    representative_chunks = find_representative_chunks(chunks, embeddings, labels, centers)
    if not representative_chunks:
        return f"No representative {chunk_type} found."
    
    print(f"Found {len(representative_chunks)} representative {chunk_type}")
    
    # Step 5: Generate final summary
    combined_text = "\n\n".join(representative_chunks)
    
    prompt = f"""Summarize the following text. This text contains key representative {chunk_type} from a larger document. 
    Please create a coherent summary that captures the main points from all {chunk_type}:
    
    {combined_text}"""
    
    try:
        print(f"Generating final summary from representative {chunk_type}...")
        response = client.chat.completions.create(
            model=agentic_model,
            temperature=0,
            messages=[
                {"role": "system",
                 "content": f"You are a precise summarizer. Create a concise but comprehensive summary that captures all the key information, main themes, and important details from the document {chunk_type}."},
                {"role": "user", "content": prompt}
            ]
        )
        
        summary = response.choices[0].message.content.strip()
        print("Summary generated successfully!")
        return summary
        
    except Exception as e:
        print(f"Error generating final summary: {e}")
        return "Failed to generate summary."


def summarize_single_pdf(pdf_path: str, num_clusters: int = 10, use_page_based: bool = True, 
                        chunk_size: int = 1000, agentic_model: str = "gpt-4o-mini") -> Dict[str, Any]:
    """Summarize a single PDF and return results."""
    try:
        print(f"\n{'='*60}")
        print(f"Processing: {os.path.basename(pdf_path)}")
        print(f"{'='*60}")
        
        # Read PDF content
        text_content = read_pdf_with_jina(pdf_path)
        
        # Generate summary
        summary = generate_summary(text_content, num_clusters, use_page_based, chunk_size, agentic_model)
        
        result = {
            "pdf_path": pdf_path,
            "filename": os.path.basename(pdf_path),
            "summary": summary,
            "timestamp": datetime.now().isoformat(),
            "success": True,
            "error": None
        }
        
        print(f"Successfully processed {os.path.basename(pdf_path)}")
        return result
        
    except Exception as e:
        error_msg = f"Error processing {pdf_path}: {str(e)}"
        print(error_msg)
        return {
            "pdf_path": pdf_path,
            "filename": os.path.basename(pdf_path),
            "summary": None,
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": error_msg
        }


def create_combined_summary(individual_summaries: List[Dict[str, Any]], 
                           agentic_model: str = "gpt-4o-mini") -> str:
    """Create a combined summary from individual PDF summaries."""
    successful_summaries = [result for result in individual_summaries if result["success"]]
    
    if not successful_summaries:
        return "No successful summaries to combine."
    
    # Combine all individual summaries
    combined_text = []
    for result in successful_summaries:
        combined_text.append(f"**{result['filename']}**:\n{result['summary']}")
    
    combined_content = "\n\n".join(combined_text)
    
    prompt = f"""Create a comprehensive summary from the following individual document summaries. 
    Please synthesize the information into a coherent overall summary that:
    1. Identifies common themes across documents
    2. Highlights key differences between documents
    3. Provides an executive overview of all documents
    4. Maintains important details and insights
    
    Individual Document Summaries:
    {combined_content}"""
    
    try:
        print("\nGenerating combined summary from all documents...")
        response = client.chat.completions.create(
            model=agentic_model,
            temperature=0,
            messages=[
                {"role": "system",
                 "content": "You are a senior analyst creating a comprehensive summary from multiple documents. Synthesize the information into a coherent, well-structured summary that captures the overall themes and key insights across all documents."},
                {"role": "user", "content": prompt}
            ]
        )
        
        combined_summary = response.choices[0].message.content.strip()
        print("Combined summary generated successfully!")
        return combined_summary
        
    except Exception as e:
        print(f"Error generating combined summary: {e}")
        return "Failed to generate combined summary."


def save_results_to_json(category: str, individual_summaries: List[Dict[str, Any]], 
                        combined_summary: str) -> str:
    """Save results to JSON file organized by category."""
    results = {
        "category": category,
        "timestamp": datetime.now().isoformat(),
        "combined_summary": combined_summary,
        "individual_summaries": individual_summaries,
        "total_documents": len(individual_summaries),
        "successful_documents": len([r for r in individual_summaries if r["success"]]),
        "failed_documents": len([r for r in individual_summaries if not r["success"]])
    }
    
    # Create filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{category}_{timestamp}.json"
    output_path = os.path.join(RESULTS_DIR, filename)
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\nResults saved to: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"Error saving results to JSON: {e}")
        return None


def summarizer_agent(pdf_paths: List[str], category: str, num_clusters: int = 10, 
                    use_page_based: bool = True, chunk_size: int = 1000, 
                    agentic_model: str = "gpt-4o-mini", max_workers: int = 3) -> Dict[str, Any]:
    """
    Main function to summarize multiple PDFs with parallel processing and JSON storage.
    
    Args:
        pdf_paths: List of PDF file paths
        category: Category for organizing results
        num_clusters: Number of clusters for summarization
        use_page_based: Whether to use page-based or character-based chunking
        chunk_size: Size of chunks for character-based chunking
        agentic_model: OpenAI model for summarization
        max_workers: Maximum number of parallel workers
    
    Returns:
        Dictionary containing results and metadata
    """
    print(f"\n{'='*80}")
    print(f"SUMMARIZER AGENT - Category: {category}")
    print(f"{'='*80}")
    print(f"Processing {len(pdf_paths)} PDF(s) with {max_workers} parallel workers")
    print(f"Method: {'Page-based' if use_page_based else 'Character-based'} chunking")
    print(f"Clusters: {num_clusters}")
    print(f"{'='*80}")
    
    # Validate PDF paths
    valid_paths = []
    for pdf_path in pdf_paths:
        if os.path.exists(pdf_path):
            valid_paths.append(pdf_path)
        else:
            print(f"Warning: PDF file not found: {pdf_path}")
    
    if not valid_paths:
        print("No valid PDF files found!")
        return {"error": "No valid PDF files found", "results": None}
    
    print(f"Processing {len(valid_paths)} valid PDF files...")
    
    # Process PDFs in parallel
    individual_summaries = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_pdf = {
            executor.submit(summarize_single_pdf, pdf_path, num_clusters, use_page_based, chunk_size, agentic_model): pdf_path
            for pdf_path in valid_paths
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_pdf):
            pdf_path = future_to_pdf[future]
            try:
                result = future.result()
                individual_summaries.append(result)
            except Exception as e:
                print(f"Error processing {pdf_path}: {e}")
                individual_summaries.append({
                    "pdf_path": pdf_path,
                    "filename": os.path.basename(pdf_path),
                    "summary": None,
                    "timestamp": datetime.now().isoformat(),
                    "success": False,
                    "error": str(e)
                })
    
    # Create combined summary
    print(f"\n{'='*80}")
    print("CREATING COMBINED SUMMARY")
    print(f"{'='*80}")
    
    combined_summary = create_combined_summary(individual_summaries, agentic_model)
    
    # Save results to JSON
    print(f"\n{'='*80}")
    print("SAVING RESULTS")
    print(f"{'='*80}")
    
    output_path = save_results_to_json(category, individual_summaries, combined_summary)
    
    # Create final result
    result = {
        "category": category,
        "total_documents": len(valid_paths),
        "successful_documents": len([r for r in individual_summaries if r["success"]]),
        "failed_documents": len([r for r in individual_summaries if not r["success"]]),
        "individual_summaries": individual_summaries,
        "combined_summary": combined_summary,
        "output_file": output_path,
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"\n{'='*80}")
    print("PROCESSING COMPLETE")
    print(f"{'='*80}")
    print(f"Total documents: {result['total_documents']}")
    print(f"Successful: {result['successful_documents']}")
    print(f"Failed: {result['failed_documents']}")
    if output_path:
        print(f"Results saved to: {output_path}")
    
    return result


if __name__ == "__main__":
    # Example usage
    pdf_paths = [
        "mp_materials/pdfs/form-10-q.pdf",
        "mp_materials/pdfs/contract.pdf",
        # Add more PDF paths here
    ]
    
    category = "financial_reports"
    
    # Run the summarizer agent
    results = summarizer_agent(
        pdf_paths=pdf_paths,
        category=category,
        num_clusters=10,
        use_page_based=False,
        chunk_size=1000,
        max_workers=3
    )
    
    # Print combined summary
    if results.get("combined_summary"):
        print(f"\n{'='*80}")
        print("COMBINED SUMMARY")
        print(f"{'='*80}")
        print(results["combined_summary"])
        print(f"{'='*80}")
 
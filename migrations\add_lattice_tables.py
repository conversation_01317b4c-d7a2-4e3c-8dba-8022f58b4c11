#!/usr/bin/env python3
"""
Database migration to add lattice functionality tables and fields

This migration adds:
1. lattice_headers table - stores headers for sessions (general and category-specific)
2. lattice_results table - stores individual lattice cell results
3. Additional fields to upload_sessions table for lattice tracking
4. New LatticeStatus enum
5. Appropriate indexes for performance

Run with: python migrations/add_lattice_tables.py
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path to import from api
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text, inspect
from api.models.database import engine, is_postgres
from api.utils.logger import logger

def run_migration():
    """Run the lattice tables migration"""
    logger.info("🚀 Starting lattice tables migration")
    
    with engine.connect() as conn:
        # Check if we're using PostgreSQL or SQLite
        dialect = conn.dialect.name
        logger.info(f"Database dialect: {dialect}")
        
        try:
            # Start transaction
            with conn.begin():
                # Check if lattice tables already exist
                inspector = inspect(engine)
                existing_tables = inspector.get_table_names()
                
                if 'lattice_headers' in existing_tables:
                    logger.warning("⚠️  Lattice tables already exist, skipping creation")
                    return
                
                # 1. Add LatticeStatus enum (PostgreSQL only)
                if is_postgres:
                    logger.info("📝 Creating LatticeStatus enum type")
                    conn.execute(text("""
                        CREATE TYPE latticestatus AS ENUM (
                            'pending',
                            'in_progress', 
                            'completed',
                            'failed'
                        );
                    """))
                
                # 2. Add lattice fields to upload_sessions table
                logger.info("📝 Adding lattice fields to upload_sessions table")
                
                if is_postgres:
                    # PostgreSQL version
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_status latticestatus DEFAULT 'pending',
                        ADD COLUMN lattice_started_at TIMESTAMP WITH TIME ZONE,
                        ADD COLUMN lattice_completed_at TIMESTAMP WITH TIME ZONE,
                        ADD COLUMN lattice_progress JSONB,
                        ADD COLUMN lattice_error_message TEXT;
                    """))
                    
                    # Add index
                    conn.execute(text("""
                        CREATE INDEX idx_session_lattice_status 
                        ON upload_sessions(lattice_status, lattice_started_at);
                    """))
                else:
                    # SQLite version
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_status TEXT DEFAULT 'pending';
                    """))
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_started_at DATETIME;
                    """))
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_completed_at DATETIME;
                    """))
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_progress TEXT;
                    """))
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        ADD COLUMN lattice_error_message TEXT;
                    """))
                
                # 3. Create lattice_headers table
                logger.info("📝 Creating lattice_headers table")
                
                if is_postgres:
                    conn.execute(text("""
                        CREATE TABLE lattice_headers (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            session_id UUID NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
                            category VARCHAR,
                            headers JSONB NOT NULL,
                            apply_to_all BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                        );
                    """))
                    
                    # Add indexes for PostgreSQL
                    conn.execute(text("""
                        CREATE INDEX idx_lattice_header_session_id ON lattice_headers(session_id);
                        CREATE INDEX idx_lattice_header_category ON lattice_headers(category);
                        CREATE INDEX idx_lattice_header_apply_all ON lattice_headers(apply_to_all);
                        CREATE INDEX idx_lattice_header_created_at ON lattice_headers(created_at);
                        CREATE INDEX idx_lattice_header_session_category ON lattice_headers(session_id, category);
                        CREATE INDEX idx_lattice_header_apply_all_session ON lattice_headers(apply_to_all, session_id);
                    """))
                else:
                    conn.execute(text("""
                        CREATE TABLE lattice_headers (
                            id TEXT PRIMARY KEY,
                            session_id TEXT NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
                            category TEXT,
                            headers TEXT NOT NULL,
                            apply_to_all BOOLEAN DEFAULT FALSE,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        );
                    """))
                    
                    # Add indexes for SQLite
                    conn.execute(text("""
                        CREATE INDEX idx_lattice_header_session_id ON lattice_headers(session_id);
                        CREATE INDEX idx_lattice_header_category ON lattice_headers(category);
                        CREATE INDEX idx_lattice_header_apply_all ON lattice_headers(apply_to_all);
                    """))
                
                # 4. Create lattice_results table
                logger.info("📝 Creating lattice_results table")
                
                if is_postgres:
                    conn.execute(text("""
                        CREATE TABLE lattice_results (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            session_id UUID NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
                            file_id UUID NOT NULL REFERENCES uploaded_files(id) ON DELETE CASCADE,
                            header_key VARCHAR NOT NULL,
                            header_category VARCHAR,
                            result_value TEXT NOT NULL,
                            result_type VARCHAR,
                            agent_type VARCHAR,
                            confidence_score NUMERIC(3,2),
                            processing_time_ms INTEGER,
                            processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            error_message TEXT
                        );
                    """))
                    
                    # Add indexes for PostgreSQL
                    conn.execute(text("""
                        CREATE INDEX idx_lattice_result_session_id ON lattice_results(session_id);
                        CREATE INDEX idx_lattice_result_file_id ON lattice_results(file_id);
                        CREATE INDEX idx_lattice_result_header_key ON lattice_results(header_key);
                        CREATE INDEX idx_lattice_result_header_category ON lattice_results(header_category);
                        CREATE INDEX idx_lattice_result_processed_at ON lattice_results(processed_at);
                        CREATE INDEX idx_lattice_lookup ON lattice_results(session_id, file_id, header_key);
                        CREATE INDEX idx_lattice_session_header ON lattice_results(session_id, header_key);
                        CREATE INDEX idx_lattice_file_header ON lattice_results(file_id, header_key);
                        CREATE INDEX idx_lattice_category_header ON lattice_results(header_category, header_key);
                        CREATE INDEX idx_lattice_result_type ON lattice_results(result_type, processed_at);
                    """))
                else:
                    conn.execute(text("""
                        CREATE TABLE lattice_results (
                            id TEXT PRIMARY KEY,
                            session_id TEXT NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
                            file_id TEXT NOT NULL REFERENCES uploaded_files(id) ON DELETE CASCADE,
                            header_key TEXT NOT NULL,
                            header_category TEXT,
                            result_value TEXT NOT NULL,
                            result_type TEXT,
                            agent_type TEXT,
                            confidence_score REAL,
                            processing_time_ms INTEGER,
                            processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            error_message TEXT
                        );
                    """))
                    
                    # Add indexes for SQLite
                    conn.execute(text("""
                        CREATE INDEX idx_lattice_result_session_id ON lattice_results(session_id);
                        CREATE INDEX idx_lattice_result_file_id ON lattice_results(file_id);
                        CREATE INDEX idx_lattice_result_header_key ON lattice_results(header_key);
                        CREATE INDEX idx_lattice_lookup ON lattice_results(session_id, file_id, header_key);
                    """))
                
                logger.info("✅ Lattice tables migration completed successfully")
                
        except Exception as e:
            logger.error(f"❌ Migration failed: {str(e)}")
            logger.error("Full error details", exc_info=True)
            raise

def rollback_migration():
    """Rollback the lattice tables migration"""
    logger.info("🔄 Rolling back lattice tables migration")
    
    with engine.connect() as conn:
        try:
            with conn.begin():
                # Drop tables in reverse order
                logger.info("🗑️  Dropping lattice_results table")
                conn.execute(text("DROP TABLE IF EXISTS lattice_results;"))
                
                logger.info("🗑️  Dropping lattice_headers table")
                conn.execute(text("DROP TABLE IF EXISTS lattice_headers;"))
                
                # Remove lattice fields from upload_sessions
                if is_postgres:
                    logger.info("🗑️  Removing lattice fields from upload_sessions")
                    conn.execute(text("""
                        ALTER TABLE upload_sessions 
                        DROP COLUMN IF EXISTS lattice_status,
                        DROP COLUMN IF EXISTS lattice_started_at,
                        DROP COLUMN IF EXISTS lattice_completed_at,
                        DROP COLUMN IF EXISTS lattice_progress,
                        DROP COLUMN IF EXISTS lattice_error_message;
                    """))
                    
                    # Drop enum type
                    conn.execute(text("DROP TYPE IF EXISTS latticestatus;"))
                else:
                    logger.warning("⚠️  SQLite doesn't support dropping columns, manual cleanup required")
                
                logger.info("✅ Rollback completed successfully")
                
        except Exception as e:
            logger.error(f"❌ Rollback failed: {str(e)}")
            logger.error("Full error details", exc_info=True)
            raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Lattice tables migration")
    parser.add_argument("--rollback", action="store_true", help="Rollback the migration")
    args = parser.parse_args()
    
    if args.rollback:
        rollback_migration()
    else:
        run_migration()
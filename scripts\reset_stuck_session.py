"""
Reset stuck categorization sessions
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger


def reset_stuck_session(session_id: str):
    """Reset a stuck categorization session"""
    
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            # Reset files stuck in "in_progress" back to "pending"
            reset_files_sql = """
            UPDATE uploaded_files
            SET 
                categorization_status = 'pending'::categorizationstatus,
                categorization_started_at = NULL
            WHERE 
                session_id::text = :session_id 
                AND categorization_status = 'in_progress'::categorizationstatus;
            """
            
            result = conn.execute(text(reset_files_sql), {"session_id": session_id})
            files_reset = result.rowcount
            
            # Reset the session state
            reset_session_sql = """
            UPDATE upload_sessions
            SET
                categorization_started_at = NULL,
                categorization_completed_at = NULL,
                categorization_total = 0,
                categorization_completed = 0,
                categorization_failed = 0
            WHERE 
                id::text = :session_id 
                AND categorization_completed_at IS NULL;
            """
            
            result = conn.execute(text(reset_session_sql), {"session_id": session_id})
            session_reset = result.rowcount
            
            conn.commit()
            
            print(f"✅ Reset stuck session:")
            print(f"   - Reset {files_reset} files from 'in_progress' to 'pending'")
            print(f"   - Reset {session_reset} session(s)")
            
            # Show current state
            status_sql = """
            SELECT 
                categorization_status,
                COUNT(*) as count
            FROM uploaded_files
            WHERE session_id::text = :session_id
            GROUP BY categorization_status;
            """
            
            result = conn.execute(text(status_sql), {"session_id": session_id})
            print(f"\nCurrent file status:")
            for row in result:
                print(f"   {row[0]}: {row[1]}")
            
            logger.info(f"Reset stuck session {session_id}: {files_reset} files, {session_reset} sessions")
            
    except Exception as e:
        logger.error(f"Error resetting stuck session: {str(e)}", exc_info=True)
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    finally:
        engine.dispose()


if __name__ == "__main__":
    session_id = "188c1893-ebdf-2f5b-b9d4-60208c451927"
    print(f"Resetting stuck session: {session_id}")
    reset_stuck_session(session_id)
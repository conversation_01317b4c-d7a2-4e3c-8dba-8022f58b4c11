import asyncio
import json
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, update

from api.models.database import UploadedFile, UploadSession, CategorizationStatus, DueDiligenceCategory, to_db_id
from api.agents.due_diligence_agent import due_diligence_agent, CategorizationResult
from api.services.pdf_extractor import pdf_extractor
from api.utils.logger import logger


class ParallelCategorizationService:
    """Service for parallel categorization of PDFs using Pydantic AI agents"""
    
    def __init__(self, batch_size: int = 5, timeout_seconds: int = 30):
        self.batch_size = batch_size
        self.timeout_seconds = timeout_seconds
        self.pdf_extractor = pdf_extractor
    
    async def categorize_session(self, session_id: str, db: Session = None):
        """
        Categorize all PDFs in a session using parallel processing
        
        Args:
            session_id: The upload session ID
            db: Database session (optional, will create new if not provided)
        """
        from api.models.database import SessionLocal
        
        # Create new session if not provided (for background tasks)
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            logger.info(f"Starting categorize_session for {session_id}")
            
            # Get session and mark categorization as started
            session = db.query(UploadSession).filter(
                UploadSession.id == to_db_id(session_id)
            ).first()
            
            if not session:
                logger.error(f"Session {session_id} not found")
                return
            
            # Get all pending files for categorization
            pending_files = db.query(UploadedFile).filter(
                UploadedFile.session_id == to_db_id(session_id),
                UploadedFile.categorization_status == CategorizationStatus.PENDING,
                UploadedFile.bucket_key.isnot(None)
            ).all()
            
            # Update session with categorization started
            session.categorization_total = len(pending_files)
            session.categorization_started_at = datetime.now(timezone.utc)
            db.commit()
            
            logger.info(f"Starting categorization for session {session_id} with {len(pending_files)} files")
            
            if len(pending_files) == 0:
                logger.warning(f"No pending files to categorize for session {session_id}")
                return
            
            # Process files in batches
            for i in range(0, len(pending_files), self.batch_size):
                batch = pending_files[i:i + self.batch_size]
                batch_files = [f.filename for f in batch]
                logger.info(f"Processing batch {i//self.batch_size + 1}: {batch_files}")
                await self._process_batch(batch, db)
                
                # Update session progress
                completed_count = db.query(UploadedFile).filter(
                    UploadedFile.session_id == to_db_id(session_id),
                    UploadedFile.categorization_status == CategorizationStatus.COMPLETED
                ).count()
                
                failed_count = db.query(UploadedFile).filter(
                    UploadedFile.session_id == to_db_id(session_id),
                    UploadedFile.categorization_status == CategorizationStatus.FAILED
                ).count()
                
                session.categorization_completed = completed_count
                session.categorization_failed = failed_count
                db.commit()
                
                logger.info(f"Session {session_id} progress: {completed_count}/{session.categorization_total} completed, {failed_count} failed")
            
            # Mark session categorization as complete
            session.categorization_completed_at = datetime.now(timezone.utc)
            db.commit()
            
            logger.info(f"Completed categorization for session {session_id}")
            
        except Exception as e:
            logger.error(f"Error in categorize_session: {str(e)}", exc_info=True)
            db.rollback()
        finally:
            if should_close_db:
                db.close()
    
    async def _process_batch(self, files: List[UploadedFile], db: Session):
        """Process a batch of files in parallel"""
        # Mark files as in progress
        file_ids = [file.id for file in files]
        db.query(UploadedFile).filter(
            UploadedFile.id.in_(file_ids)
        ).update({
            "categorization_status": CategorizationStatus.IN_PROGRESS,
            "categorization_started_at": datetime.now(timezone.utc)
        }, synchronize_session=False)
        db.commit()
        
        # Create categorization tasks
        tasks = []
        for file in files:
            task = self._categorize_single_file(file)
            tasks.append(task)
        
        # Run tasks in parallel with timeout
        try:
            logger.info(f"Running {len(tasks)} categorization tasks in parallel")
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.timeout_seconds * len(files)
            )
            
            # Process results and update database
            success_count = 0
            fail_count = 0
            for file, result in zip(files, results):
                if isinstance(result, Exception):
                    # Handle failed categorization
                    fail_count += 1
                    logger.error(f"Failed to categorize {file.filename}: {str(result)}")
                    self._update_file_failed(file, str(result), db)
                else:
                    # Handle successful categorization
                    success_count += 1
                    self._update_file_success(file, result, db)
            
            logger.info(f"Batch complete: {success_count} succeeded, {fail_count} failed")
            db.commit()
            
        except asyncio.TimeoutError:
            logger.error(f"Batch processing timed out after {self.timeout_seconds * len(files)} seconds")
            # Mark remaining in-progress files as failed
            db.query(UploadedFile).filter(
                UploadedFile.id.in_(file_ids),
                UploadedFile.categorization_status == CategorizationStatus.IN_PROGRESS
            ).update({
                "categorization_status": CategorizationStatus.FAILED,
                "agent_insights": {"error": "Categorization timed out"}
            }, synchronize_session=False)
            db.commit()
    
    async def _categorize_single_file(self, file: UploadedFile) -> Dict[str, Any]:
        """Categorize a single file using the due diligence agent"""
        try:
            # Extract first page text
            first_page_text = self.pdf_extractor.extract_first_page_text(file.bucket_key)
            
            if not first_page_text:
                # Fallback to filename-only categorization
                prompt = f"""Categorize this document based on its filename.
                
FILENAME: {file.filename}
"""
            else:
                # Full categorization with content
                prompt = f"""Analyze this document and categorize it into the appropriate due diligence category.

FILENAME: {file.filename}

DOCUMENT CONTENT (First Page):
{first_page_text[:3000]}"""
            
            # Run the agent
            result = await due_diligence_agent.run(prompt)
            categorization_result = result.data
            
            # Return structured result
            return {
                "category": categorization_result.category,
                "confidence": categorization_result.confidence,
                "insights": {
                    "reasoning": categorization_result.reasoning,
                    "key_indicators": categorization_result.key_indicators,
                    "usage": {
                        "requests": result.usage().requests,
                        "request_tokens": result.usage().request_tokens,
                        "response_tokens": result.usage().response_tokens,
                        "total_tokens": result.usage().total_tokens
                    } if hasattr(result, 'usage') and result.usage() else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error categorizing file {file.filename}: {str(e)}", exc_info=True)
            raise
    
    def _update_file_success(self, file: UploadedFile, result: Dict[str, Any], db: Session):
        """Update file with successful categorization results"""
        try:
            file.due_diligence_category = DueDiligenceCategory(result["category"])
            file.categorization_confidence = result["confidence"]
            file.categorization_status = CategorizationStatus.COMPLETED
            file.categorized_at = datetime.now(timezone.utc)
            file.agent_insights = result["insights"]
            
            # Store metadata
            file.categorization_metadata = {
                "categorized_at": datetime.now(timezone.utc).isoformat(),
                "first_page_extracted": True,
                "has_usage_info": result["insights"].get("usage") is not None
            }
            
            logger.info(f"Successfully categorized {file.filename} as {result['category']} with confidence {result['confidence']}")
            
        except Exception as e:
            logger.error(f"Error updating file success: {str(e)}", exc_info=True)
            self._update_file_failed(file, str(e), db)
    
    def _update_file_failed(self, file: UploadedFile, error: str, db: Session):
        """Update file with failed categorization"""
        file.categorization_status = CategorizationStatus.FAILED
        file.agent_insights = {"error": error}
        logger.error(f"Failed to categorize {file.filename}: {error}")


# Singleton instance
parallel_categorization_service = ParallelCategorizationService()
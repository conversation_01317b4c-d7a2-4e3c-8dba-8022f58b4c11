from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import time
import uuid

from api.config import settings
from api.utils.logger import logger, setup_logger
from api.routers import upload, storage, process
from api.routers import dev as dev_router


# Setup logger
setup_logger(level=settings.log_level)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting FastAPI Upload API")
    yield
    # Shutdown
    logger.info("Shutting down FastAPI Upload API")


# Create FastAPI app
app = FastAPI(
    title="Quantera Upload API",
    description="API for handling bulk document uploads with job polling",
    version="1.0.0",
    lifespan=lifespan,
    docs_url=f"{settings.api_prefix}/docs",
    redoc_url=f"{settings.api_prefix}/redoc",
    openapi_url=f"{settings.api_prefix}/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Add request ID middleware
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())
    
    # Store in request state
    request.state.request_id = request_id
    
    # Add to response headers
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "detail": "Resource not found",
            "path": str(request.url.path)
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    logger.error(f"Internal server error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "request_id": getattr(request.state, "request_id", "unknown")
        }
    )


# Health check endpoint
@app.get(f"{settings.api_prefix}/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "upload-api",
        "version": "1.0.0"
    }


# Include routers
app.include_router(upload.router, prefix=settings.api_prefix)
app.include_router(storage.router, prefix=settings.api_prefix)
app.include_router(process.router, prefix=settings.api_prefix)

# Include dev router only in development mode
if settings.environment == "dev":
    app.include_router(dev_router.router, prefix=settings.api_prefix)
    logger.info("Development mode enabled - dev endpoints available")
else:
    logger.info("Running in production mode - dev endpoints disabled")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
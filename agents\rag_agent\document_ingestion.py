import logging
import traceback
import uuid
import os
import asyncio
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from dotenv import load_dotenv
import requests

from fastembed import TextEmbedding, SparseTextEmbedding
from qdrant_client import QdrantClient
from qdrant_client.models import (
    VectorParams,
    Distance,
    SparseVectorParams,
    PointStruct,
    HnswConfigDiff,
    Modifier,
    KeywordIndexParams,
    SparseVector,
)
from llama_cloud_services import LlamaParse
from openai import OpenAI
from langchain_text_splitters import RecursiveCharacterTextSplitter

load_dotenv()
logger = logging.getLogger(__name__)


class DocumentIngestion:
    """Document ingestion class for processing and storing documents with embeddings in Qdrant."""
    
    def __init__(self):
        self._sparse_embedding_model = None
        self._dense_embedding_model = None
        self._openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self._memory_graph_url = "http://localhost:8000"
    
    def init_sparse_embedding_model(self) -> None:
        """
        Initialize sparse embedding model during app startup.
        """
        if self._sparse_embedding_model is None:
            try:
                logger.info("Initializing sparse embedding model...")
                self._sparse_embedding_model = SparseTextEmbedding(
                    "Qdrant/bm25", 
                    cache_dir="model_cache",
                )
                logger.info("Sparse embedding model initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize sparse embedding model: {str(e)}")
                raise RuntimeError(f"Failed to initialize sparse embedding model: {str(e)}")

    def init_dense_embedding_model(self) -> None:
        """
        Initialize dense embedding model during app startup.
        """
        if self._dense_embedding_model is None:
            try:
                logger.info("Initializing dense embedding model...")
                model_name = "nomic-ai/nomic-embed-text-v1.5-Q"
                self._dense_embedding_model = TextEmbedding(model_name=model_name)
                logger.info("Dense embedding model initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize dense embedding model: {str(e)}")
                raise RuntimeError(f"Failed to initialize dense embedding model: {str(e)}")

    async def pdf_to_text(self, file_paths):
        """
        Parse documents using LlamaParse and return the results.
        """
        parser = LlamaParse(
            api_key=os.getenv("LLAMAPARSE_API_KEY"),
            num_workers=4,
            verbose=True,
            language="en",
        )
        
        # Handle both single file and batch processing
        if isinstance(file_paths, str):
            # Single file
            result = await parser.aparse(file_paths)
            return result
        else:
            # Multiple files
            results = await parser.aparse(file_paths)
            return results

    def parse_output(self, parse_results: List) -> Tuple[str, str]:
        """
        Get a simple tuple of (markdown, plain_text) from parsed documents.
        """
        markdown_parts = []
        text_parts = []
        
        for job_result in parse_results:
            for page in job_result.pages:
                # Add page number header for markdown
                markdown_parts.append(f"{page.md}")
                
                # Add page number header for text
                text_parts.append(f"{page.text}")
        
        markdown_content = '\n\n---\n\n'.join(markdown_parts)
        text_content = ('\n\n' + '='*50 + '\n\n').join(text_parts)
        
        return (markdown_content, text_content)

    def create_summary(self, text: str) -> str:
        """
        Create a summary of the text.
        """
        response = self._openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a skilled financial analyst. Analyze the financial document and provide a clear summary covering key financial performance, position, risks, facts, figures and important metrics with specific numbers. Focus on revenue trends, profitability, balance sheet strength, and cash flow. Cover all the key information in the document."},
                {"role": "user", "content": text}
            ]
        )
        return response.choices[0].message.content

    def _chunk_text(self, text: str, chunk_size: int = 1280, chunk_overlap: int = 256) -> List[str]:
        """
        Split text into chunks using LangChain's RecursiveCharacterTextSplitter.
        """
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=[],
        )
        chunks = text_splitter.split_text(text)
        return chunks

    def ingest_summaries_to_graph(self, summary: str, doc_id: str, user_id: str, doc_name: str) -> bool:
        """
        Ingest document summaries to the memory graph via API call.
        """
        try:
            logger.info("Ingesting summary to memory graph...")
            
            # Prepare the payload
            payload = {
                "content": summary,
                "metadata": {
                    "labels": [],
                    "properties": [
                        {"doc_id": doc_id},
                        {"user_id": user_id},
                        {"doc_name": doc_name}
                    ]
                }
            }
            
            # Make the API call to the memory graph insert endpoint
            response = requests.post(
                f"{self._memory_graph_url}/api/insert",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            # Check if the request was successful
            if response.status_code in [200, 202]:
                if response.status_code == 200:
                    logger.info("Successfully ingested summary to memory graph")
                elif response.status_code == 202:
                    response_data = response.json()
                    task_id = response_data.get('task_id', 'unknown')
                    logger.info(f"Successfully queued summary for memory graph processing (Task ID: {task_id})")
                return True
            else:
                logger.error(f"Failed to ingest summary to memory graph. Status code: {response.status_code}, Response: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            logger.error("Failed to connect to memory graph API. Make sure the service is running on localhost:8000")
            return False
        except requests.exceptions.Timeout:
            logger.error("Request to memory graph API timed out")
            return False
        except Exception as e:
            logger.error(f"Error ingesting summary to memory graph: {str(e)}")
            logger.debug(f"Memory graph ingestion traceback: {traceback.format_exc()}")
            return False

    def get_sparse_embeddings(self, text: str) -> Optional[SparseVector]:
        """
        Get sparse embeddings for a single text string using the sparse model.
        """
        if self._sparse_embedding_model is None:
            logger.error("Sparse embedding model not initialized. Call init_sparse_embedding_model() first.")
            return None
        
        try:
            # Generate sparse embeddings
            sparse_embeddings = list(self._sparse_embedding_model.embed([text]))
            if sparse_embeddings:
                sparse_emb = sparse_embeddings[0]
                # Convert SparseEmbedding to SparseVector format
                return SparseVector(
                    indices=sparse_emb.indices.tolist(),
                    values=sparse_emb.values.tolist()
                )
            return None
        except Exception as e:
            logger.error(f"Sparse embedding generation failed: {str(e)}")
            logger.debug(f"Sparse embedding traceback: {traceback.format_exc()}")
            return None

    def get_dense_embeddings(self, text: str) -> Optional[List[float]]:
        """
        Get dense embeddings for a single text string using FastEmbed.
        """
        if self._dense_embedding_model is None:
            logger.error("Dense embedding model not initialized. Call init_dense_embedding_model() first.")
            return None
        
        try:
            # Generate embeddings and convert generator to list
            embeddings = list(self._dense_embedding_model.embed([text]))
            
            # Convert numpy array to list for consistent return type
            if embeddings:
                return embeddings[0].tolist()
            return None
        except ImportError:
            logger.error("fastembed package not installed. Run 'pip install fastembed' for FastEmbed support.")
            return None
        except Exception as e:
            logger.error(f"Dense embedding generation failed: {str(e)}")
            logger.debug(f"Dense embedding traceback: {traceback.format_exc()}")
            return None

    def initialize_qdrant_collection(
        self,
        collection_name: str, 
        qdrant_client: QdrantClient,
        dense_vector_size: int = 768
    ) -> bool:
        """
        Initialize Qdrant collection if it doesn't exist.
        """
        try:
            logger.info(f"Initializing Qdrant collection: {collection_name}")
            
            # Check if collection exists
            collections = qdrant_client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if collection_name not in collection_names:
                qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "dense": VectorParams(
                            size=dense_vector_size, 
                            distance=Distance.COSINE
                        )
                    },
                    sparse_vectors_config={
                        "sparse": SparseVectorParams(
                            modifier=Modifier.IDF,
                        )
                    },
                    hnsw_config=HnswConfigDiff(
                        payload_m=16,  # Configure local index for payload-based filtering
                        m=0,  # Disable global index
                    ),
                )
                
                # Create index for the user_id field to optimize user-based queries
                qdrant_client.create_payload_index(
                    collection_name=collection_name,
                    field_name="user_id",
                    field_schema=KeywordIndexParams(
                        type="keyword",
                        is_tenant=True,  # Optimize for tenant-based queries
                    ),
                )
                logger.info(f"Collection '{collection_name}' created successfully.")
            else:
                logger.info(f"Collection '{collection_name}' already exists.")

            return True
        except Exception as e:
            logger.error(f"Failed to initialize collection '{collection_name}': {str(e)}")
            logger.debug(f"Collection initialization traceback: {traceback.format_exc()}")
            return False

    def ingest_document_to_qdrant(
        self,
        text: str,
        doc_id: str,
        user_id: str,
        doc_name: str,
        collection_name: str,
        qdrant_client: QdrantClient,
        chunk_size: int = 1280,
        chunk_overlap: int = 256
    ) -> bool:
        """
        Ingest a document into Qdrant database by chunking and creating embeddings.
        """
        try:
            logger.info(f"Ingesting document {doc_id} for user {user_id}")
            
            # Split text into chunks
            chunks = self._chunk_text(text, chunk_size, chunk_overlap)
            
            # Get embeddings for all chunks
            points = []
            
            for chunk in chunks:
                # Get dense embeddings
                dense_embedding = self.get_dense_embeddings(chunk)
                if not dense_embedding:
                    logger.error(f"Failed to get dense embeddings for chunk in document {doc_id}")
                    continue
                
                # Get sparse embeddings
                sparse_embedding = self.get_sparse_embeddings(chunk)
                if not sparse_embedding:
                    logger.error(f"Failed to get sparse embeddings for chunk in document {doc_id}")
                    continue
                
                # Create point
                points.append(
                    PointStruct(
                        id=str(uuid.uuid4()),
                        vector={
                            "dense": dense_embedding,
                            "sparse": sparse_embedding,
                        },
                        payload={
                            "doc_id": doc_id,
                            "user_id": user_id,
                            "doc_name": doc_name,
                            "text": chunk,
                        },
                    )
                )
            
            if not points:
                logger.error(f"No valid points created for document {doc_id}")
                return False
            
            # Upload all points to Qdrant
            qdrant_client.upsert(collection_name=collection_name, points=points)
            logger.info(f"Successfully ingested {len(points)} chunks for document {doc_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error ingesting document {doc_id}: {str(e)}")
            logger.debug(f"Document ingestion traceback: {traceback.format_exc()}")
            return False

    def get_dense_embeddings_batch(self, texts: List[str]) -> Optional[List[List[float]]]:
        """
        Get dense embeddings for multiple texts efficiently in batch.
        """
        if self._dense_embedding_model is None:
            logger.error("Dense embedding model not initialized. Call init_dense_embedding_model() first.")
            return None
        
        try:
            # Generate embeddings and convert generator to list
            all_embeddings = list(self._dense_embedding_model.embed(texts))
            
            # Convert numpy arrays to lists for consistent return type
            all_embeddings = [emb.tolist() for emb in all_embeddings]
            
            return all_embeddings
        except ImportError:
            logger.error("fastembed package not installed. Run 'pip install fastembed' for FastEmbed support.")
            return None
        except Exception as e:
            logger.error(f"Batch dense embedding generation failed: {str(e)}")
            logger.debug(f"Batch dense embedding traceback: {traceback.format_exc()}")
            return None

    def get_sparse_embeddings_batch(self, texts: List[str]) -> Optional[List[SparseVector]]:
        """
        Get sparse embeddings for multiple texts efficiently in batch.
        """
        if self._sparse_embedding_model is None:
            logger.error("Sparse embedding model not initialized. Call init_sparse_embedding_model() first.")
            return None
        
        try:
            # Generate sparse embeddings
            sparse_embeddings = list(self._sparse_embedding_model.embed(texts))
            # Convert each SparseEmbedding to SparseVector format
            return [
                SparseVector(
                    indices=sparse_emb.indices.tolist(),
                    values=sparse_emb.values.tolist()
                )
                for sparse_emb in sparse_embeddings
            ]
        except Exception as e:
            logger.error(f"Batch sparse embedding generation failed: {str(e)}")
            logger.debug(f"Batch sparse embedding traceback: {traceback.format_exc()}")
            return None
        
    
    
async def run_ingestion_pipeline(
    file_path: str,
    doc_id: str = None,
    user_id: str = "default_user",
    collection_name: str = "documents",
    qdrant_url: str = "http://localhost:6333",
    chunk_size: int = 1280,
    chunk_overlap: int = 256
) -> Tuple[bool, bool]:
    """
    Complete document ingestion pipeline from PDF to both vector and graph storage.
    
    Args:
        file_path: Path to the PDF file to process
        doc_id: Unique document identifier (auto-generated if None)
        user_id: User identifier for the document
        collection_name: Qdrant collection name
        qdrant_url: Qdrant database URL
        chunk_size: Size of text chunks for vector storage
        chunk_overlap: Overlap between chunks
        
    Returns:
        Tuple of (qdrant_success, graph_success) booleans
    """
    try:
        logger.info(f"Starting document ingestion pipeline for: {file_path}")
        
        # Initialize document ingestion class
        doc_ingestion = DocumentIngestion()
        
        # Initialize embedding models
        doc_ingestion.init_sparse_embedding_model()
        doc_ingestion.init_dense_embedding_model()
        
        # Initialize Qdrant client
        qdrant_client = QdrantClient(url=qdrant_url)
        
        # Initialize Qdrant collection
        collection_success = doc_ingestion.initialize_qdrant_collection(
            collection_name=collection_name,
            qdrant_client=qdrant_client
        )
        
        if not collection_success:
            logger.error("Failed to initialize Qdrant collection")
            return False, False
        
        # Generate doc_id if not provided
        if doc_id is None:
            doc_id = str(uuid.uuid4())
        
        # Step 1: Parse PDF to text
        logger.info("Step 1: Parsing PDF to text...")
        parse_results = await doc_ingestion.pdf_to_text(file_path)
        
        # Ensure parse_results is always a list
        if not isinstance(parse_results, list):
            parse_results = [parse_results]
        
        # Step 2: Parse output to get plain text
        logger.info("Step 2: Extracting plain text...")
        markdown, plain_text = doc_ingestion.parse_output(parse_results)
        
        if not plain_text.strip():
            logger.error("No text extracted from PDF")
            return False, False
        
        # Step 3: Create summary
        logger.info("Step 3: Creating document summary...")
        summary = doc_ingestion.create_summary(markdown)
        
        # Step 4: Ingest summary to memory graph
        logger.info("Step 4: Ingesting summary to memory graph...")
        graph_success = doc_ingestion.ingest_summaries_to_graph(summary, doc_id, user_id, file_path)
        
        # Step 5: Ingest document chunks to Qdrant
        logger.info("Step 5: Ingesting document chunks to Qdrant...")
        qdrant_success = doc_ingestion.ingest_document_to_qdrant(
            text=plain_text,
            doc_id=doc_id,
            user_id=user_id,
            doc_name=file_path,
            collection_name=collection_name,
            qdrant_client=qdrant_client,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # Log results
        logger.info(f"Pipeline completed - Qdrant: {'Success' if qdrant_success else 'Failed'}, Graph: {'Success' if graph_success else 'Failed'}")
        
        return qdrant_success, graph_success
        
    except Exception as e:
        logger.error(f"Error in document ingestion pipeline: {str(e)}")
        logger.debug(f"Pipeline traceback: {traceback.format_exc()}")
        return False, False


def run_ingestion_pipeline_sync(file_path: str, **kwargs) -> Tuple[bool, bool]:
    """
    Synchronous wrapper for the async run_ingestion_pipeline function.
    
    Args:
        file_path: Path to the PDF file to process
        **kwargs: Additional arguments passed to run_ingestion_pipeline
        
    Returns:
        Tuple of (qdrant_success, graph_success) booleans
    """
    return asyncio.run(run_ingestion_pipeline(file_path, **kwargs))

if __name__ == "__main__":
    run_ingestion_pipeline_sync("data/revenue-bridge.pdf")
# Quantera API Documentation

## Table of Contents
- [Upload Endpoints](#upload-endpoints)
  - [1. Prepare Upload](#1-prepare-upload)
  - [2. Complete Upload](#2-complete-upload) 
  - [3. Process Files](#3-process-files)
  - [4. Get Session Status](#4-get-session-status)
  - [5. Get Processing Results](#5-get-processing-results)
- [Process Endpoints](#process-endpoints)
- [Storage Endpoints](#storage-endpoints)
- [Development Endpoints](#development-endpoints)

---

## Upload Endpoints

Base URL: `/api/v1/upload`

### 1. Prepare Upload

**Endpoint:** `POST /api/v1/upload/prepare`

**Description:** Prepare for file uploads by creating a session and generating presigned URLs

#### Parameters

| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `user_id` | string | query | No | User identifier |
| `company_name` | string | query | No | Company name for the upload session |
| `session_type` | string | query | No | Type of upload session |

#### Request Body

```json
{
  "filenames": [
    "document1.pdf",
    "document2.pdf", 
    "report.xlsx"
  ]
}
```

**Schema:**
- `filenames` (array of strings, required): List of filenames to upload (min: 1, max: 1000)

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "7b22178d-faa6-4657-b0b6-6d73efe51af6",
  "presigned_urls": [
    {
      "filename": "document1.pdf",
      "upload_url": "https://quanteraai.sfo3.digitaloceanspaces.com/quanteraai/uploads/7b22178d-faa6-4657-b0b6-6d73efe51af6/20250722_133736_document1.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=DO801FNFB6QXKM3BU862%2F20250722%2Fsfo3%2Fs3%2Faws4_request&X-Amz-Date=20250722T133736Z&X-Amz-Expires=900&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Signature=ca69b9fe49177db310bff6dcf5c31f1a96ab5173bd739fce13a48770bf9a0f2b",
      "file_id": "bf22ade9-34f1-41b7-a6b5-85402c06b6a0",
      "bucket_key": "uploads/7b22178d-faa6-4657-b0b6-6d73efe51af6/20250722_133736_document1.pdf"
    }
  ],
  "expires_in_seconds": 900
}
```

**Response Schema:**
- `session_id` (string): Unique session identifier
- `presigned_urls` (array): Array of presigned URL objects
  - `filename` (string): Original filename
  - `upload_url` (string): Presigned URL for uploading the file
  - `file_id` (string): Unique file identifier
  - `bucket_key` (string): Storage bucket key for the file
- `expires_in_seconds` (integer): URL expiration time in seconds

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/upload/prepare?user_id=user123&company_name=ACME%20Corp&session_type=due_diligence' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "filenames": [
    "document1.pdf",
    "document2.pdf",
    "report.xlsx"
  ]
}'
```

#### Error Responses

| Status Code | Description |
|-------------|-------------|
| `422` | Validation Error - Invalid request body |
| `500` | Internal Server Error - Failed to create session or generate URLs |

---

### 2. Complete Upload

**Endpoint:** `POST /api/v1/upload/complete`

**Description:** Mark files as uploaded and update their metadata

#### Request Body

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "files": [
    {
      "filename": "document1.pdf",
      "bucket_id": "uploads/123e4567/document1.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf"
    }
  ]
}
```

**Schema:**
- `session_id` (string, required): Session identifier from prepare upload
- `files` (array, required): Array of uploaded file information
  - `filename` (string): Original filename
  - `bucket_id` (string): Bucket identifier
  - `file_size` (integer, optional): File size in bytes
  - `content_type` (string, optional): MIME type of the file

#### Response

**Status Code:** `200 OK`

Returns a `SessionStatusResponse` with updated file statuses.

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/upload/complete' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "files": [
    {
      "filename": "document1.pdf",
      "bucket_id": "uploads/123e4567/document1.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf"
    }
  ]
}'
```

---

### 3. Process Files

**Endpoint:** `POST /api/v1/upload/process/{session_id}`

**Description:** Start processing files in a session

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "processing",
  "message": "Processing started for 3 files"
}
```

**Response Schema:**
- `session_id` (string): Session identifier
- `status` (string): Processing status
- `message` (string): Status message

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/upload/process/123e4567-e89b-12d3-a456-426614174000' \
  -H 'accept: application/json'
```

---

### 4. Get Session Status

**Endpoint:** `GET /api/v1/upload/status/{session_id}`

**Description:** Get the current status of an upload session

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "processing",
  "total_files": 3,
  "processed_files": 1,
  "created_at": "2024-01-14T10:00:00Z",
  "updated_at": "2024-01-14T10:05:00Z",
  "files": [
    {
      "id": "file_123",
      "filename": "document1.pdf",
      "status": "processed",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "created_at": "2024-01-14T10:00:00Z"
    }
  ]
}
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/upload/status/123e4567-e89b-12d3-a456-426614174000' \
  -H 'accept: application/json'
```

---

### 5. Get Processing Results

**Endpoint:** `GET /api/v1/upload/results/{session_id}`

**Description:** Get detailed processing results for a session

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

Returns detailed session information with processing results.

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/upload/results/123e4567-e89b-12d3-a456-426614174000' \
  -H 'accept: application/json'
```

---

## Common Response Schemas

### SessionStatusResponse

```json
{
  "session_id": "string",
  "status": "pending|processing|completed|failed",
  "total_files": 0,
  "processed_files": 0,
  "created_at": "2024-01-14T10:00:00Z",
  "updated_at": "2024-01-14T10:00:00Z",
  "files": [
    {
      "id": "string",
      "filename": "string",
      "status": "pending|uploaded|processed|failed",
      "file_size": 0,
      "content_type": "string",
      "created_at": "2024-01-14T10:00:00Z",
      "error_message": "string"
    }
  ],
  "error_message": "string"
}
```

### ErrorResponse

```json
{
  "detail": "Error message",
  "error_code": "ERROR_CODE"
}
```

---

## Storage Endpoints

Base URL: `/api/v1/storage`

### 1. List Objects

**Endpoint:** `GET /api/v1/storage/list`

**Description:** List objects in the storage bucket with optional filtering

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prefix` | string | No | None | Prefix to filter objects |
| `limit` | integer | No | 100 | Maximum number of objects to return (1-1000) |
| `continuation_token` | string | No | None | Token for pagination |

#### Response

**Status Code:** `200 OK`

```json
{
  "objects": [
    {
      "key": "uploads/session-123/document1.pdf",
      "size": 1024000,
      "last_modified": "2024-01-14T10:00:00Z",
      "etag": "d41d8cd98f00b204e9800998ecf8427e"
    }
  ],
  "count": 1,
  "is_truncated": false,
  "total_size_mb": 0.98,
  "next_continuation_token": "token_if_truncated"
}
```

**Response Schema:**
- `objects` (array): Array of storage objects
  - `key` (string): Object key/path
  - `size` (integer): Object size in bytes
  - `last_modified` (string): Last modification timestamp
  - `etag` (string): Entity tag
- `count` (integer): Number of objects returned
- `is_truncated` (boolean): Whether more objects exist
- `total_size_mb` (number): Total size in megabytes
- `next_continuation_token` (string, optional): Token for next page

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/storage/list?prefix=uploads/&limit=50' \
  -H 'accept: application/json'
```

---

### 2. Get Object Info

**Endpoint:** `GET /api/v1/storage/info/{object_key}`

**Description:** Get detailed information about a specific object

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `object_key` | string | Yes | Full path/key of the object |

#### Response

**Status Code:** `200 OK`

```json
{
  "key": "uploads/session-123/document1.pdf",
  "size": 1024000,
  "content_type": "application/pdf",
  "last_modified": "2024-01-14T10:00:00Z",
  "etag": "d41d8cd98f00b204e9800998ecf8427e",
  "url": "https://quanteraai.sfo3.digitaloceanspaces.com/quanteraai/uploads/session-123/document1.pdf"
}
```

**Response Schema:**
- `key` (string): Object key/path
- `size` (integer): Object size in bytes
- `content_type` (string): MIME type
- `last_modified` (string): Last modification timestamp
- `etag` (string): Entity tag
- `url` (string): Public URL for the object

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/storage/info/uploads/session-123/document1.pdf' \
  -H 'accept: application/json'
```

#### Error Responses

| Status Code | Description |
|-------------|-------------|
| `404` | Object not found |
| `500` | Internal Server Error |

---

### 3. Generate Presigned URL

**Endpoint:** `GET /api/v1/storage/presigned-url/{object_key}`

**Description:** Generate a presigned URL for accessing an object

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `object_key` | string | Yes | Full path/key of the object |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `expiration` | integer | No | 3600 | URL expiration in seconds (60-86400) |
| `download` | boolean | No | false | Generate download URL instead of view URL |

#### Response

**Status Code:** `200 OK`

```json
{
  "url": "https://quanteraai.sfo3.digitaloceanspaces.com/quanteraai/uploads/session-123/document1.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&...",
  "expires_in_seconds": 3600,
  "object_key": "uploads/session-123/document1.pdf"
}
```

**Response Schema:**
- `url` (string): Presigned URL for accessing the object
- `expires_in_seconds` (integer): URL expiration time in seconds
- `object_key` (string): Object key that was requested

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/storage/presigned-url/uploads/session-123/document1.pdf?expiration=7200&download=true' \
  -H 'accept: application/json'
```

---

### 4. Get Storage Statistics

**Endpoint:** `GET /api/v1/storage/stats`

**Description:** Get storage statistics and usage information

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prefix` | string | No | None | Prefix to filter statistics |

#### Response

**Status Code:** `200 OK`

```json
{
  "total_objects": 1500,
  "total_size_mb": 2048.5,
  "total_size_gb": 2.0,
  "prefix_breakdown": {
    "uploads": {
      "count": 1200,
      "size": 1843200000
    },
    "temp": {
      "count": 300,
      "size": 204800000
    }
  },
  "bucket_name": "quanteraai"
}
```

**Response Schema:**
- `total_objects` (integer): Total number of objects
- `total_size_mb` (number): Total size in megabytes
- `total_size_gb` (number): Total size in gigabytes
- `prefix_breakdown` (object): Statistics grouped by prefix
- `bucket_name` (string): Storage bucket name

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/storage/stats?prefix=uploads/' \
  -H 'accept: application/json'
```

---

### 5. Cleanup Session

**Endpoint:** `POST /api/v1/storage/cleanup-session/{session_id}`

**Description:** Delete all objects associated with a specific upload session

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

```json
{
  "deleted": true,
  "count": 5,
  "message": "Successfully cleaned up 5 objects for session 123e4567-e89b-12d3-a456-426614174000"
}
```

**Response Schema:**
- `deleted` (boolean): Whether deletion was successful
- `count` (integer): Number of objects deleted
- `message` (string): Status message

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/storage/cleanup-session/123e4567-e89b-12d3-a456-426614174000' \
  -H 'accept: application/json'
```

#### Error Responses

| Status Code | Description |
|-------------|-------------|
| `404` | Session not found |
| `500` | Internal Server Error |

---

## File Upload Workflow

1. **Prepare Upload**: Call `/upload/prepare` with list of filenames
2. **Upload Files**: Use the presigned URLs to upload files directly to storage
3. **Complete Upload**: Call `/upload/complete` to confirm files are uploaded
4. **Process Files**: Call `/upload/process/{session_id}` to start processing
5. **Monitor Progress**: Poll `/upload/status/{session_id}` for progress updates
6. **Get Results**: Call `/upload/results/{session_id}` for final results

## Storage Management Workflow

1. **List Objects**: Use `/storage/list` to browse stored files
2. **Get Object Info**: Use `/storage/info/{object_key}` for detailed file information
3. **Generate Access URLs**: Use `/storage/presigned-url/{object_key}` for secure file access
4. **Monitor Usage**: Use `/storage/stats` to track storage usage
5. **Cleanup**: Use `/storage/cleanup-session/{session_id}` to remove session files

---

## Process Endpoints

Base URL: `/api/v1/process`

### 1. Start Processing

**Endpoint:** `POST /api/v1/process/{session_id}`

**Description:** Start processing for a session with optional lattice headers for advanced analysis

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Request Body (Optional)

```json
{
  "headers": ["Revenue", "Expenses", "Assets"],
  "headers_by_category": {
    "financial": ["EBITDA", "Cash Flow", "Debt Ratio"],
    "legal": ["Contract Terms", "Liability", "Compliance"]
  }
}
```

**Schema:**
- `headers` (array of strings, optional): General headers applied to all documents
- `headers_by_category` (object, optional): Category-specific headers
  - Key: category name (string)
  - Value: array of headers for that category

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "processing",
  "message": "Processing started with lattice analysis",
  "categorization_status": "completed",
  "lattice_status": "in_progress",
  "poll_endpoint": "/api/v1/process/123e4567-e89b-12d3-a456-426614174000/status"
}
```

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/process/123e4567-e89b-12d3-a456-426614174000' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "headers": ["Revenue", "Expenses", "Assets"],
  "headers_by_category": {
    "financial": ["EBITDA", "Cash Flow"],
    "legal": ["Contract Terms", "Compliance"]
  }
}'
```

---

### 2. Get Processing Status

**Endpoint:** `GET /api/v1/process/{session_id}/status`

**Description:** Get comprehensive processing status including categorization and lattice progress

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "categorization_status": {
    "pending": 0,
    "in_progress": 0,
    "completed": 15,
    "failed": 0
  },
  "category_counts": {
    "financial": 8,
    "legal": 4,
    "operational": 3
  },
  "lattice_status": "completed",
  "lattice_progress": {
    "financial": {
      "completed": 8,
      "total": 8,
      "percentage": 100
    },
    "legal": {
      "completed": 4,
      "total": 4,
      "percentage": 100
    }
  },
  "overall_progress": {
    "categorization_complete": true,
    "lattice_complete": true,
    "total_files": 15,
    "processed_files": 15
  }
}
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/process/123e4567-e89b-12d3-a456-426614174000/status' \
  -H 'accept: application/json'
```

---

### 3. Get Lattice Matrix

**Endpoint:** `GET /api/v1/process/{session_id}/lattice`

**Description:** Get the complete lattice analysis matrix for a session

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "lattice_status": "completed",
  "matrix": {
    "headers": ["Revenue", "Expenses", "Assets"],
    "categories": {
      "financial": {
        "files": ["10-K.pdf", "earnings.pdf"],
        "results": [
          ["$100M", "Not found", "$500M"],
          ["$95M", "$80M", "Not found"]
        ]
      }
    }
  },
  "metadata": {
    "total_headers": 3,
    "total_categories": 2,
    "total_files": 15,
    "completion_percentage": 100
  }
}
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/process/123e4567-e89b-12d3-a456-426614174000/lattice' \
  -H 'accept: application/json'
```

---

### 4. Get Files by Category

**Endpoint:** `GET /api/v1/process/{session_id}/category/{category}`

**Description:** Get all files in a session that belong to a specific due diligence category

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `session_id` | string | Yes | Upload session identifier |
| `category` | string | Yes | Due diligence category (financial, legal, operational, etc.) |

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "123e4567-e89b-12d3-a456-426614174000",
  "category": "financial",
  "files": [
    {
      "filename": "10-K.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "bucket_key": "uploads/session-123/10-K.pdf",
      "categorization_confidence": 0.95,
      "categorized_at": "2024-01-14T10:00:00Z",
      "insights": {
        "summary": "Annual financial report",
        "key_metrics": ["Revenue", "Net Income"]
      }
    }
  ],
  "total_files": 1
}
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/process/123e4567-e89b-12d3-a456-426614174000/category/financial' \
  -H 'accept: application/json'
```

---

## Development Endpoints

Base URL: `/api/v1/dev`

> **Note:** These endpoints are only available when the application is running in development mode. They allow processing local files without going through the upload workflow.

### 1. Process Local Files

**Endpoint:** `POST /api/v1/dev/process`

**Description:** Process files from local directories (mp_materials/resido) and upload to storage, bypassing the manual upload flow

#### Request Body

```json
{
  "source": "mp_materials",
  "file_types": ["pdf", "xlsx"],
  "subdirectory": "Investor Relations",
  "auto_process": true,
  "user_id": "dev_user"
}
```

**Schema:**
- `source` (string, optional): Source directory - "mp_materials", "resido", or "both" (default: "both")
- `file_types` (array of strings, optional): File types to process - ["pdf", "xls", "xlsx"]
- `subdirectory` (string, optional): Specific subdirectory to scan
- `auto_process` (boolean, optional): Automatically trigger processing after upload (default: true)
- `user_id` (string, optional): User identifier for the session

#### Response

**Status Code:** `200 OK`

```json
{
  "session_id": "188c1893-ebdf-2f5b-b9d4-60208c451927",
  "status": "processing",
  "message": "Processing started for 25 files from mp_materials",
  "files_uploaded": 25,
  "cached": false,
  "poll_endpoint": "/api/v1/process/188c1893-ebdf-2f5b-b9d4-60208c451927/status"
}
```

**Response Schema:**
- `session_id` (string): Session identifier for tracking progress
- `status` (string): Current status
- `message` (string): Status message
- `files_uploaded` (integer): Number of files uploaded
- `cached` (boolean): Whether results were cached from previous run
- `poll_endpoint` (string): Endpoint to poll for progress

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/dev/process' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "source": "mp_materials",
  "file_types": ["pdf", "xlsx"],
  "auto_process": true
}'
```

---

### 2. List Local Files

**Endpoint:** `GET /api/v1/dev/list-local-files`

**Description:** List all files that would be processed without actually uploading them

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `source` | string | No | "both" | Source directory: "mp_materials", "resido", or "both" |
| `file_types` | string | No | None | Comma-separated file types (pdf,xls,xlsx) |
| `subdirectory` | string | No | None | Specific subdirectory to scan |

#### Response

**Status Code:** `200 OK`

```json
[
  {
    "filename": "10-K.pdf",
    "full_path": "/Users/<USER>/Desktop/Quantera/mp_materials/pdfs/10-K.pdf",
    "size": 1024000,
    "extension": ".pdf",
    "directory": "mp_materials/pdfs",
    "source": "mp_materials"
  }
]
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/dev/list-local-files?source=mp_materials&file_types=pdf,xlsx' \
  -H 'accept: application/json'
```

---

### 3. Process Specific Files

**Endpoint:** `POST /api/v1/dev/process-specific-files`

**Description:** Process specific files by providing their full paths

#### Request Body

```json
{
  "file_paths": [
    "/path/to/document1.pdf",
    "/path/to/document2.xlsx"
  ],
  "user_id": "dev_user",
  "auto_process": true
}
```

#### cURL Example

```bash
curl -X 'POST' \
  'http://0.0.0.0:8000/api/v1/dev/process-specific-files' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "file_paths": ["/path/to/document1.pdf"],
  "auto_process": true
}'
```

---

### 4. Get Development Statistics

**Endpoint:** `GET /api/v1/dev/stats`

**Description:** Get statistics about available local files

#### Response

**Status Code:** `200 OK`

```json
{
  "mp_materials": {
    "total_files": 150,
    "pdf_files": 120,
    "excel_files": 30,
    "total_size_mb": 2048.5,
    "directories": ["pdfs", "Investor Relations/pdfs"]
  },
  "resido": {
    "total_files": 200,
    "pdf_files": 180,
    "excel_files": 20,
    "total_size_mb": 1536.2,
    "directories": ["pdfs", "Financial Database/pdfs"]
  },
  "combined": {
    "total_files": 350,
    "total_size_mb": 3584.7
  }
}
```

#### cURL Example

```bash
curl -X 'GET' \
  'http://0.0.0.0:8000/api/v1/dev/stats' \
  -H 'accept: application/json'
```

---

## Development Workflow (Skip Upload)

1. **List Available Files**: Use `/dev/list-local-files` to see what's available
2. **Process Local Files**: Use `/dev/process` to upload and process files automatically
3. **Get Session ID**: Use the returned session ID with any process endpoint
4. **Monitor Progress**: Poll `/process/{session_id}/status` for progress
5. **Get Results**: Use `/process/{session_id}/lattice` for final analysis

**Example Session ID from Postman:** `188c1893-ebdf-2f5b-b9d4-60208c451927`

This workflow is perfect for development and testing since it bypasses the manual upload process and works with your local file directories.

---

## Rate Limits & Constraints

- Maximum 1000 files per upload session
- Presigned URLs expire in 900 seconds (15 minutes) for uploads, configurable for downloads
- Supported file types: PDF, XLS, XLSX
- Maximum file size: Determined by storage provider limits
- Storage list operations limited to 1000 objects per request (use pagination for more)
- Development endpoints only available in dev mode
- Local file processing depends on available disk space and file system permissions 
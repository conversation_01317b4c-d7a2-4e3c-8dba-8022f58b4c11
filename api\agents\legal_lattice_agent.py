import os
import re
import time
import asyncio
from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field, create_model
from pydantic_ai import Agent
from dotenv import load_dotenv

from api.utils.logger import logger

# Load environment variables
load_dotenv()

def create_legal_lattice_model(headers: List[str]) -> type[BaseModel]:
    """
    Dynamically create a Pydantic model based on legal lattice headers
    
    Args:
        headers: List of legal analysis headers
        
    Returns:
        Dynamically created Pydantic model class
    """
    logger.info(f"Creating legal lattice model with {len(headers)} headers")
    logger.debug(f"Legal headers: {headers}")
    
    fields = {}
    
    for header in headers:
        # Convert header to valid Python field name
        field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
        field_name = re.sub(r'_+', '_', field_name).strip('_')
        
        # Ensure field name doesn't start with number
        if field_name and field_name[0].isdigit():
            field_name = f"header_{field_name}"
        
        # All fields are treated equally - let the AI agent decide the appropriate format
        fields[field_name] = (
            str, 
            Field(
                ...,
                description=f"Legal analysis for: {header}. Be ultra-concise (max 50 chars). Use 'Not Found' if no relevant information.",
                max_length=50,
                example="Delaware, USA or 30-day notice or Not Found"
            )
        )
    
    model_class = create_model('LegalLatticeOutput', **fields)
    logger.info(f"Created dynamic Pydantic model '{model_class.__name__}' with {len(fields)} fields")
    
    return model_class


def create_legal_lattice_agent(headers: List[str]) -> Agent:
    """
    Create a Pydantic AI agent for legal lattice analysis
    
    Args:
        headers: List of legal headers to analyze
        
    Returns:
        Configured Pydantic AI agent
    """
    start_time = time.time()
    logger.info(f"🏛️  Creating legal lattice agent with {len(headers)} headers")
    logger.debug(f"Legal agent headers: {headers}")
    
    # Create dynamic result model
    ResultModel = create_legal_lattice_model(headers)
    
    # Create detailed system prompt for legal analysis
    system_prompt = f"""You are an expert legal analyst with extensive experience in contract analysis, compliance, and legal document review.

LATTICE HEADERS TO ANALYZE:
{chr(10).join(f'- {h}' for h in headers)}

CRITICAL INSTRUCTIONS:
1. For each header, provide ULTRA-CONCISE answers (max 50 characters)
2. Focus on specific legal facts, clauses, and provisions from the document
3. Use exact legal terminology when found in the document
4. For jurisdictions: provide location (e.g., "Delaware, USA", "New York", "UK")
5. For clauses: provide brief description (e.g., "30-day notice", "Mutual termination")
6. For compliance: specify standards/regulations (e.g., "GDPR compliant", "SOX compliant")
7. Use "Not Found" if information is not explicitly stated in the document
8. DO NOT make legal assumptions - only report what's clearly stated

EXAMPLES OF GOOD RESPONSES:
- Governing Law: "Delaware, USA"
- Termination Clause: "30-day notice required"
- Liability Limitation: "$1M cap on damages"
- IP Ownership: "Company retains all rights"
- Compliance Standard: "SOC 2 Type II"
- If not found: "Not Found"

DOCUMENT ANALYSIS APPROACH:
1. Read the entire document content carefully
2. Look for legal clauses, provisions, and specific legal language
3. Extract exact information for each header
4. Be precise and factual - avoid interpretation
5. Prioritize information explicitly stated in the document
"""
    
    # Get API configuration
    api_key = os.getenv("OPENAI_API_KEY")
    model_name = os.getenv("OPENAI_MODEL_NAME", "gpt-4")
    
    if not api_key:
        api_key = os.getenv("AGENTIC_API_KEY")
        model_name = "agentic-large"
        base_url = os.getenv("AGENTIC_BASE_URL")
        logger.info("Using Agentic API for legal agent")
    else:
        base_url = None
        logger.info("Using OpenAI API for legal agent")
    
    # Create agent with appropriate configuration
    model_settings = {
        'temperature': 0.1,  # Low temperature for consistent legal analysis
        'max_tokens': 1000,  # Enough for all fields
    }
    
    if api_key:
        model_settings['api_key'] = api_key
    if base_url:
        model_settings['base_url'] = base_url
    
    agent = Agent(
        model=f'openai:{model_name}' if not base_url else f'openai:{model_name}',
        result_type=ResultModel,
        system_prompt=system_prompt,
        model_settings=model_settings
    )
    
    creation_time = time.time() - start_time
    logger.info(f"✅ Legal lattice agent created successfully in {creation_time:.2f}s")
    logger.debug(f"Agent model: {model_name}, Temperature: 0.1")
    
    return agent


async def process_legal_document(
    agent: Agent, 
    content: str, 
    filename: str,
    headers: List[str]
) -> Dict[str, Any]:
    """
    Process a legal document with detailed logging
    
    Args:
        agent: The Pydantic AI agent
        content: Document content
        filename: Name of the file being processed
        headers: List of headers being analyzed
        
    Returns:
        Dictionary with processing results and metadata
    """
    start_time = time.time()
    logger.info(f"🔍 Starting legal analysis for document: {filename}")
    logger.debug(f"Document content length: {len(content)} characters")
    logger.debug(f"Headers to analyze: {headers}")
    
    try:
        # Prepare input for agent
        prompt = f"Filename: {filename}\n\nLegal Document Content:\n{content[:4000]}"
        logger.debug(f"Prepared prompt for {filename} (truncated to 4000 chars)")
        
        # Run agent analysis
        logger.debug(f"Starting agent analysis for {filename}")
        agent_start = time.time()
        
        result = await agent.run(prompt)
        
        agent_time = time.time() - agent_start
        logger.info(f"✅ Legal agent analysis completed for {filename} in {agent_time:.2f}s")
        
        # Analyze results
        field_results = {}
        populated_fields = 0
        not_found_fields = 0
        
        for header in headers:
            field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
            field_name = re.sub(r'_+', '_', field_name).strip('_')
            
            if field_name and field_name[0].isdigit():
                field_name = f"header_{field_name}"
            
            value = getattr(result.data, field_name, "Not Found")
            field_results[header] = value
            
            if value and str(value).lower() not in ['not found', 'not applicable', 'n/a', '']:
                populated_fields += 1
                logger.debug(f"Legal result: {header} = {value}")
            else:
                not_found_fields += 1
                logger.debug(f"Legal result: {header} = Not Found")
        
        total_time = time.time() - start_time
        success_rate = (populated_fields / len(headers)) * 100 if headers else 0
        
        logger.info(f"✅ Legal document {filename} completed in {total_time:.2f}s")
        logger.info(f"📊 Legal results: {populated_fields}/{len(headers)} fields populated ({success_rate:.1f}% success rate)")
        
        return {
            "status": "success",
            "filename": filename,
            "processing_time": total_time,
            "agent_time": agent_time,
            "fields_populated": populated_fields,
            "fields_not_found": not_found_fields,
            "success_rate": success_rate,
            "results": field_results,
            "raw_result": result
        }
        
    except asyncio.TimeoutError:
        total_time = time.time() - start_time
        logger.warning(f"⏰ Legal analysis timeout for {filename} after {total_time:.2f}s")
        return {
            "status": "timeout",
            "filename": filename,
            "processing_time": total_time,
            "error": "Processing timeout"
        }
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"❌ Legal analysis error for {filename} after {total_time:.2f}s: {str(e)}")
        logger.error(f"Legal analysis error details for {filename}", exc_info=True)
        return {
            "status": "error",
            "filename": filename,
            "processing_time": total_time,
            "error": str(e)
        }


def normalize_legal_field_name(header: str) -> str:
    """
    Normalize header to valid Python field name for legal fields
    
    Args:
        header: Original header string
        
    Returns:
        Normalized field name
    """
    field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
    field_name = re.sub(r'_+', '_', field_name).strip('_')
    
    if field_name and field_name[0].isdigit():
        field_name = f"header_{field_name}"
    
    return field_name


# Test function for development
async def test_legal_agent():
    """Test function for legal agent development"""
    test_headers = [
        "Governing Law Jurisdiction",
        "Termination Clause Summary", 
        "Liability Limitations",
        "IP Ownership Status",
        "Compliance Requirements"
    ]
    
    agent = create_legal_lattice_agent(test_headers)
    
    test_content = """
    SAMPLE LEGAL CONTRACT
    
    This Agreement shall be governed by the laws of Delaware, United States.
    Either party may terminate this agreement with 30 days written notice.
    Liability shall not exceed $1,000,000 in aggregate.
    All intellectual property rights remain with the Company.
    This agreement complies with SOC 2 Type II standards.
    """
    
    result = await process_legal_document(agent, test_content, "test_contract.pdf", test_headers)
    print(f"Test result: {result}")


if __name__ == "__main__":
    asyncio.run(test_legal_agent())
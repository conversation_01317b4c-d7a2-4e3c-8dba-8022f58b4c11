import asyncio
import hashlib
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.database import (
    UploadSession, UploadedFile, FileProcessingResult,
    UploadStatus, FileStatus, to_db_id, from_db_id
)
from api.services.storage import storage_service
from api.utils.logger import logger


class FileProcessor:
    """Handles file processing logic"""
    
    async def process_session(self, session_id: str, db: Session = None) -> None:
        """Process all files in a session"""
        # Create new database session if not provided
        if db is None:
            from api.models.database import SessionLocal
            db = SessionLocal()
            close_db = True
        else:
            close_db = False
            
        try:
            # Get session
            session = db.query(UploadSession).filter(UploadSession.id == to_db_id(session_id)).first()
            if not session:
                logger.error(f"Session {session_id} not found")
                return
            
            # Update session status to processing
            session.status = UploadStatus.PROCESSING
            db.commit()
            
            logger.info(f"Starting processing for session {session_id} with {session.total_files} files")
            
            # Process each file
            processed_count = 0
            failed_count = 0
            total_size = 0
            
            for file in session.files:
                if file.status == FileStatus.UPLOADED:
                    success = await self.process_file(file, db)
                    if success:
                        processed_count += 1
                        if file.file_size:
                            total_size += file.file_size
                    else:
                        failed_count += 1
            
            # Update session status
            session.processed_files = processed_count
            session.total_size_bytes = total_size
            
            if failed_count > 0:
                session.status = UploadStatus.FAILED
                session.error_message = f"{failed_count} files failed to process"
            else:
                session.status = UploadStatus.COMPLETED
            
            # Update session metadata if PostgreSQL
            if hasattr(session.session_metadata, '__class__') and session.session_metadata.__class__.__name__ == 'str':
                # SQLite - store as JSON string
                metadata = json.loads(session.session_metadata or '{}')
            else:
                # PostgreSQL - JSONB
                metadata = session.session_metadata or {}
            
            metadata.update({
                "processing_completed_at": datetime.utcnow().isoformat(),
                "processed_files": processed_count,
                "failed_files": failed_count,
                "total_size_bytes": total_size
            })
            
            session.session_metadata = json.dumps(metadata) if isinstance(session.session_metadata, str) else metadata
            db.commit()
            
            logger.info(f"Completed processing for session {session_id}. "
                       f"Processed: {processed_count}, Failed: {failed_count}")
            
        except Exception as e:
            logger.error(f"Error processing session {session_id}: {str(e)}", exc_info=True)
            if 'session' in locals() and session:
                session.status = UploadStatus.FAILED
                session.error_message = str(e)
                db.commit()
        finally:
            if close_db:
                db.close()
    
    async def process_file(self, file: UploadedFile, db: Session) -> bool:
        """Process a single file"""
        try:
            logger.info(f"Processing file {file.filename} (ID: {file.id})")
            
            # Update status to processing
            file.status = FileStatus.PROCESSING
            db.commit()
            
            # Verify file exists in storage
            if not storage_service.check_object_exists(file.bucket_key):
                file.status = FileStatus.FAILED
                file.error_message = "File not found in storage"
                db.commit()
                return False
            
            # Get file metadata
            metadata = storage_service.get_object_metadata(file.bucket_key)
            if metadata:
                file.file_size = metadata['size']
                file.content_type = metadata['content_type']
            
            # Generate file hash (would need actual file content in production)
            file.file_hash = hashlib.sha256(f"{file.bucket_key}:{file.file_size}".encode()).hexdigest()
            
            # Create processing result record
            processing_result = FileProcessingResult(
                file_id=file.id,
                processing_type="initial_processing",
                processing_metadata=json.dumps({
                    "start_time": datetime.utcnow().isoformat(),
                    "file_type": file.content_type,
                    "file_size": file.file_size
                }) if not hasattr(FileProcessingResult.processing_metadata.type, 'python_type') else {
                    "start_time": datetime.utcnow().isoformat(),
                    "file_type": file.content_type,
                    "file_size": file.file_size
                }
            )
            db.add(processing_result)
            
            # Simulate processing delay (replace with actual processing logic)
            await asyncio.sleep(1)
            
            # Here you would add your actual file processing logic:
            # 1. Download file content from S3
            # 2. Extract text from PDFs
            # 3. Run AI analysis
            # 4. Extract entities
            # 5. Generate embeddings
            # 6. Store in Qdrant
            
            # Mock processing results
            processing_result.extracted_text = f"Sample extracted text from {file.filename}"
            processing_result.ai_summary = f"This is a summary of {file.filename}"
            
            # Mock entity extraction
            entities = {
                "companies": ["Company A", "Company B"],
                "people": ["John Doe", "Jane Smith"],
                "dates": ["2024-01-14", "2024-02-15"],
                "amounts": ["$1,000,000", "$500,000"]
            }
            processing_result.entities = json.dumps(entities) if isinstance(processing_result.entities, str) else entities
            
            # Mock classifications
            classifications = {
                "document_type": "financial_report",
                "confidence": 0.95,
                "categories": ["quarterly_report", "earnings"]
            }
            processing_result.classifications = json.dumps(classifications) if isinstance(processing_result.classifications, str) else classifications
            
            # Mark embeddings as generated (mock)
            processing_result.embeddings_generated = True
            processing_result.qdrant_point_ids = json.dumps([str(uuid.uuid4()) for _ in range(3)]) if isinstance(processing_result.qdrant_point_ids, str) else [str(uuid.uuid4()) for _ in range(3)]
            
            # Update processing metadata
            end_metadata = {
                "end_time": datetime.utcnow().isoformat(),
                "processing_duration_seconds": 1,
                "status": "completed"
            }
            if isinstance(processing_result.processing_metadata, str):
                current_metadata = json.loads(processing_result.processing_metadata)
                current_metadata.update(end_metadata)
                processing_result.processing_metadata = json.dumps(current_metadata)
            else:
                processing_result.processing_metadata.update(end_metadata)
            
            # Mark file as processed
            file.status = FileStatus.PROCESSED
            file.processed_at = datetime.utcnow()
            
            # Store file metadata
            file_metadata = {
                "processing_completed": True,
                "entities_extracted": len(entities.get("companies", [])) + len(entities.get("people", [])),
                "text_length": len(processing_result.extracted_text or ""),
                "document_type": classifications.get("document_type")
            }
            file.file_metadata = json.dumps(file_metadata) if isinstance(file.file_metadata, str) else file_metadata
            
            db.commit()
            
            logger.info(f"Successfully processed file {file.filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}", exc_info=True)
            file.status = FileStatus.FAILED
            file.error_message = str(e)
            db.commit()
            return False
    
    def get_session_info(self, session_id: str, db: Session) -> dict:
        """Get detailed information about a session and its files"""
        session = db.query(UploadSession).filter(UploadSession.id == to_db_id(session_id)).first()
        if not session:
            return None
        
        file_stats = {
            FileStatus.PENDING: 0,
            FileStatus.UPLOADED: 0,
            FileStatus.PROCESSING: 0,
            FileStatus.PROCESSED: 0,
            FileStatus.FAILED: 0
        }
        
        processing_results = []
        
        for file in session.files:
            file_stats[file.status] += 1
            
            # Get processing results for each file
            for result in file.processing_results:
                result_data = {
                    "file_id": from_db_id(file.id),
                    "filename": file.filename,
                    "processing_type": result.processing_type,
                    "has_extracted_text": bool(result.extracted_text),
                    "has_ai_summary": bool(result.ai_summary),
                    "embeddings_generated": result.embeddings_generated,
                    "created_at": result.created_at.isoformat() if result.created_at else None
                }
                
                # Parse JSON fields
                if result.entities:
                    entities = json.loads(result.entities) if isinstance(result.entities, str) else result.entities
                    result_data["entity_counts"] = {
                        k: len(v) for k, v in entities.items()
                    }
                
                if result.classifications:
                    classifications = json.loads(result.classifications) if isinstance(result.classifications, str) else result.classifications
                    result_data["document_type"] = classifications.get("document_type")
                    result_data["classification_confidence"] = classifications.get("confidence")
                
                processing_results.append(result_data)
        
        return {
            "session_id": from_db_id(session.id),
            "status": session.status,
            "user_id": session.user_id,
            "company_name": session.company_name,
            "session_type": session.session_type,
            "total_files": session.total_files,
            "processed_files": session.processed_files,
            "total_size_bytes": session.total_size_bytes,
            "file_stats": file_stats,
            "processing_results": processing_results,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "error_message": session.error_message
        }
    
    async def get_processing_results(self, file_id: str, db: Session) -> List[Dict[str, Any]]:
        """Get all processing results for a specific file"""
        results = db.query(FileProcessingResult).filter(
            FileProcessingResult.file_id == file_id
        ).all()
        
        output = []
        for result in results:
            data = {
                "id": result.id,
                "processing_type": result.processing_type,
                "extracted_text": result.extracted_text,
                "ai_summary": result.ai_summary,
                "embeddings_generated": result.embeddings_generated,
                "created_at": result.created_at.isoformat() if result.created_at else None
            }
            
            # Parse JSON fields
            if result.entities:
                data["entities"] = json.loads(result.entities) if isinstance(result.entities, str) else result.entities
            
            if result.classifications:
                data["classifications"] = json.loads(result.classifications) if isinstance(result.classifications, str) else result.classifications
            
            if result.qdrant_point_ids:
                data["qdrant_point_ids"] = json.loads(result.qdrant_point_ids) if isinstance(result.qdrant_point_ids, str) else result.qdrant_point_ids
            
            output.append(data)
        
        return output


# Import uuid for mock data
import uuid

# Singleton instance
file_processor = FileProcessor()
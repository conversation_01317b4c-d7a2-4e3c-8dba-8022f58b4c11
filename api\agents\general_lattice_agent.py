import os
import re
import time
import asyncio
from typing import List, Dict, Any
from pydantic import BaseModel, Field, create_model
from pydantic_ai import Agent
from dotenv import load_dotenv

from api.utils.logger import logger

# Load environment variables
load_dotenv()

def create_general_lattice_model(headers: List[str]) -> type[BaseModel]:
    """
    Dynamically create a Pydantic model based on general lattice headers
    
    Args:
        headers: List of general analysis headers (mixed domain)
        
    Returns:
        Dynamically created Pydantic model class
    """
    logger.info(f"Creating general lattice model with {len(headers)} headers")
    logger.debug(f"General headers: {headers}")
    
    fields = {}
    
    for header in headers:
        # Convert header to valid Python field name
        field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
        field_name = re.sub(r'_+', '_', field_name).strip('_')
        
        # Ensure field name doesn't start with number
        if field_name and field_name[0].isdigit():
            field_name = f"field_{field_name}"
        
        # All fields are treated equally - let the AI agent decide the appropriate format
        fields[field_name] = (
            str,
            Field(
                ...,
                description=f"Analysis result for: {header}. Be ultra-concise (max 50 chars). Use 'Not Found' if no information available, 'Not Applicable' if doesn't apply to this document type.",
                max_length=50,
                example="Specific answer or Not Found or Not Applicable"
            )
        )
    
    model_class = create_model('GeneralLatticeOutput', **fields)
    logger.info(f"Created dynamic Pydantic model '{model_class.__name__}' with {len(fields)} fields")
    
    return model_class


def create_general_lattice_agent(headers: List[str]) -> Agent:
    """
    Create a Pydantic AI agent for general lattice analysis (mixed domains)
    
    Args:
        headers: List of headers to analyze (can be from any domain)
        
    Returns:
        Configured Pydantic AI agent
    """
    start_time = time.time()
    logger.info(f"🔍 Creating general lattice agent with {len(headers)} headers")
    logger.debug(f"General agent headers: {headers}")
    
    # Create dynamic result model
    ResultModel = create_general_lattice_model(headers)
    
    # Create detailed system prompt for general analysis
    system_prompt = f"""You are an expert document analyst with broad knowledge across financial, legal, technical, operational, and market domains.

LATTICE HEADERS TO ANALYZE:
{chr(10).join(f'- {h}' for h in headers)}

CRITICAL INSTRUCTIONS:
1. For each header, provide ULTRA-CONCISE answers (max 50 characters)
2. Analyze the document and extract relevant information for each header
3. Determine the most appropriate response format based on the content and header type
4. Use "Not Found" if the information is not present in the document
5. Use "Not Applicable" if the header doesn't apply to this document type
6. DO NOT make assumptions - only report what's explicitly stated

RESPONSE APPROACH:
- For financial data: include units (%, $, x) - e.g., "22.5%", "$2.3M"
- For legal information: be specific - e.g., "Delaware, USA", "30-day notice"
- For technical details: be concise - e.g., "AWS, Python", "SOC 2 Type II"
- For qualitative assessments: be brief - e.g., "Strong growth", "High risk"
- For missing information: "Not Found"
- For inapplicable headers: "Not Applicable"

DOCUMENT ANALYSIS STRATEGY:
1. Read the entire document content carefully
2. For each header, search for relevant information throughout the document
3. Some headers may seem unrelated to the document type - still attempt to find information
4. Be precise and factual - extract only what's stated
5. If unsure whether information applies, lean toward "Not Found" rather than guessing
"""
    
    # Get API configuration
    api_key = os.getenv("OPENAI_API_KEY")
    model_name = os.getenv("OPENAI_MODEL_NAME", "gpt-4")
    
    if not api_key:
        api_key = os.getenv("AGENTIC_API_KEY")
        model_name = "agentic-large"
        base_url = os.getenv("AGENTIC_BASE_URL")
        logger.info("Using Agentic API for general agent")
    else:
        base_url = None
        logger.info("Using OpenAI API for general agent")
    
    # Create agent with appropriate configuration
    model_settings = {
        'temperature': 0.1,  # Low temperature for consistent analysis
        'max_tokens': 1200,  # Extra tokens for mixed domain analysis
    }
    
    if api_key:
        model_settings['api_key'] = api_key
    if base_url:
        model_settings['base_url'] = base_url
    
    agent = Agent(
        model=f'openai:{model_name}' if not base_url else f'openai:{model_name}',
        result_type=ResultModel,
        system_prompt=system_prompt,
        model_settings=model_settings
    )
    
    creation_time = time.time() - start_time
    logger.info(f"✅ General lattice agent created successfully in {creation_time:.2f}s")
    logger.debug(f"Agent model: {model_name}, Temperature: 0.1")
    
    return agent


async def process_general_document(
    agent: Agent, 
    content: str, 
    filename: str,
    headers: List[str],
    document_category: str = "unknown"
) -> Dict[str, Any]:
    """
    Process a document with general headers using detailed logging
    
    Args:
        agent: The Pydantic AI agent
        content: Document content
        filename: Name of the file being processed
        headers: List of headers being analyzed
        document_category: Category of the document (for logging context)
        
    Returns:
        Dictionary with processing results and metadata
    """
    start_time = time.time()
    logger.info(f"🔍 Starting general analysis for document: {filename} (category: {document_category})")
    logger.debug(f"Document content length: {len(content)} characters")
    logger.debug(f"Headers to analyze: {headers}")
    
    try:
        # Prepare input for agent with document category context
        prompt = f"Filename: {filename}\nDocument Category: {document_category}\n\nDocument Content:\n{content[:4000]}"
        logger.debug(f"Prepared prompt for {filename} (truncated to 4000 chars)")
        
        # Run agent analysis
        logger.debug(f"Starting agent analysis for {filename}")
        agent_start = time.time()
        
        result = await agent.run(prompt)
        
        agent_time = time.time() - agent_start
        logger.info(f"✅ General agent analysis completed for {filename} in {agent_time:.2f}s")
        
        # Analyze results
        field_results = {}
        populated_fields = 0
        not_found_fields = 0
        not_applicable_fields = 0
        
        for header in headers:
            field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
            field_name = re.sub(r'_+', '_', field_name).strip('_')
            
            if field_name and field_name[0].isdigit():
                field_name = f"field_{field_name}"
            
            value = getattr(result.data, field_name, "Not Found")
            field_results[header] = value
            
            # Categorize results
            if value and str(value).lower() == 'not applicable':
                not_applicable_fields += 1
                logger.debug(f"General result: {header} = Not Applicable")
            elif value and str(value).lower() not in ['not found', 'not applicable', 'n/a', '']:
                populated_fields += 1
                logger.debug(f"General result: {header} = {value}")
            else:
                not_found_fields += 1
                logger.debug(f"General result: {header} = Not Found")
        
        total_time = time.time() - start_time
        success_rate = (populated_fields / len(headers)) * 100 if headers else 0
        applicable_rate = ((populated_fields + not_found_fields) / len(headers)) * 100 if headers else 0
        
        logger.info(f"✅ General document {filename} completed in {total_time:.2f}s")
        logger.info(f"📊 General results: {populated_fields}/{len(headers)} populated, {not_applicable_fields} N/A ({success_rate:.1f}% success, {applicable_rate:.1f}% applicable)")
        
        return {
            "status": "success",
            "filename": filename,
            "document_category": document_category,
            "processing_time": total_time,
            "agent_time": agent_time,
            "fields_populated": populated_fields,
            "fields_not_found": not_found_fields,
            "fields_not_applicable": not_applicable_fields,
            "success_rate": success_rate,
            "applicable_rate": applicable_rate,
            "results": field_results,
            "raw_result": result
        }
        
    except asyncio.TimeoutError:
        total_time = time.time() - start_time
        logger.warning(f"⏰ General analysis timeout for {filename} after {total_time:.2f}s")
        return {
            "status": "timeout",
            "filename": filename,
            "document_category": document_category,
            "processing_time": total_time,
            "error": "Processing timeout"
        }
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"❌ General analysis error for {filename} after {total_time:.2f}s: {str(e)}")
        logger.error(f"General analysis error details for {filename}", exc_info=True)
        return {
            "status": "error",
            "filename": filename,
            "document_category": document_category,
            "processing_time": total_time,
            "error": str(e)
        }


def normalize_general_field_name(header: str) -> str:
    """
    Normalize header to valid Python field name for general fields
    
    Args:
        header: Original header string
        
    Returns:
        Normalized field name
    """
    field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
    field_name = re.sub(r'_+', '_', field_name).strip('_')
    
    if field_name and field_name[0].isdigit():
        field_name = f"field_{field_name}"
    
    return field_name


def analyze_general_results(results: Dict[str, str], document_category: str) -> Dict[str, Any]:
    """
    Analyze general results for insights about cross-domain information
    
    Args:
        results: Dictionary of header -> value mappings
        document_category: The document's primary category
        
    Returns:
        Analysis insights
    """
    logger.debug(f"Analyzing general results for {document_category} document")
    
    insights = {
        "document_category": document_category,
        "cross_domain_findings": 0,
        "domain_specific_findings": 0,
        "not_applicable_count": 0,
        "populated_headers": [],
        "cross_domain_examples": []
    }
    
    for header, value in results.items():
        if value and str(value).lower() == 'not applicable':
            insights["not_applicable_count"] += 1
        elif value and str(value).lower() not in ['not found', 'not applicable', 'n/a']:
            insights["populated_headers"].append(header)
            
            # This would be cross-domain if we found useful info in an unexpected document type
            # But we're avoiding any keyword matching logic per user's instruction
            insights["cross_domain_findings"] += 1
    
    logger.debug(f"General analysis insights: {insights}")
    return insights


# Test function for development
async def test_general_agent():
    """Test function for general agent development"""
    test_headers = [
        "Revenue Growth Rate",
        "Legal Jurisdiction", 
        "Technology Stack",
        "Market Share",
        "Compliance Status",
        "Risk Assessment"
    ]
    
    agent = create_general_lattice_agent(test_headers)
    
    test_content = """
    MIXED DOCUMENT CONTENT
    
    This document contains various information types:
    - Revenue grew 15% year-over-year in 2023
    - Governed by Delaware state law
    - Built on AWS infrastructure with Python backend
    - Holds approximately 5% market share in the sector
    - Compliant with SOC 2 Type II standards
    - Risk level assessed as moderate due to market volatility
    """
    
    result = await process_general_document(agent, test_content, "test_mixed.pdf", test_headers, "general")
    print(f"Test result: {result}")
    
    if result["status"] == "success":
        insights = analyze_general_results(result["results"], "general")
        print(f"General insights: {insights}")


if __name__ == "__main__":
    asyncio.run(test_general_agent())
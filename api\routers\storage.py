from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from api.models.database import get_db, UploadSession, UploadedFile, to_db_id, from_db_id
from api.services.storage import storage_service
from api.config import settings
from api.utils.logger import logger


# Request/Response models
class StorageObject(BaseModel):
    key: str
    size: int
    last_modified: str
    etag: Optional[str] = None


class StorageInfo(BaseModel):
    key: str
    size: int
    content_type: str
    last_modified: str
    etag: str
    url: str


class DeleteResponse(BaseModel):
    deleted: bool
    key: Optional[str] = None
    count: Optional[int] = None
    message: str


# Create router
router = APIRouter(prefix="/storage", tags=["storage"])


@router.get("/list")
async def list_objects(
    prefix: Optional[str] = Query(None, description="Prefix to filter objects"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of objects to return"),
    continuation_token: Optional[str] = Query(None, description="Token for pagination")
) -> Dict[str, Any]:
    """
    List objects in the storage bucket
    """
    try:
        # Build parameters
        params = {
            'Bucket': storage_service.bucket_name,
            'MaxKeys': limit
        }
        
        if prefix:
            params['Prefix'] = prefix
        
        if continuation_token:
            params['ContinuationToken'] = continuation_token
        
        # List objects
        response = storage_service.client.list_objects_v2(**params)
        
        # Format response
        objects = []
        for obj in response.get('Contents', []):
            objects.append(StorageObject(
                key=obj['Key'],
                size=obj['Size'],
                last_modified=obj['LastModified'].isoformat(),
                etag=obj.get('ETag', '').strip('"')
            ))
        
        result = {
            'objects': objects,
            'count': len(objects),
            'is_truncated': response.get('IsTruncated', False),
            'total_size_mb': sum(obj.size for obj in objects) / (1024 * 1024)
        }
        
        if response.get('NextContinuationToken'):
            result['next_continuation_token'] = response['NextContinuationToken']
        
        return result
        
    except Exception as e:
        logger.error(f"Error listing objects: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/info/{object_key:path}")
async def get_object_info(object_key: str) -> StorageInfo:
    """
    Get detailed information about a specific object
    """
    try:
        metadata = storage_service.get_object_metadata(object_key)
        
        if not metadata:
            raise HTTPException(status_code=404, detail="Object not found")
        
        # Generate URL
        url = f"{storage_service.client._endpoint.host}/{storage_service.bucket_name}/{object_key}"
        
        return StorageInfo(
            key=object_key,
            size=metadata['size'],
            content_type=metadata['content_type'],
            last_modified=metadata['last_modified'].isoformat() if metadata['last_modified'] else "",
            etag=metadata['etag'],
            url=url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting object info: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete")
async def delete_object(
    object_key: Optional[str] = Query(None, description="Specific object key to delete"),
    prefix: Optional[str] = Query(None, description="Delete all objects with this prefix")
) -> DeleteResponse:
    """
    Delete a specific object or all objects with a prefix
    """
    try:
        if not object_key and not prefix:
            raise HTTPException(
                status_code=400,
                detail="Either object_key or prefix must be provided"
            )
        
        if object_key and prefix:
            raise HTTPException(
                status_code=400,
                detail="Cannot specify both object_key and prefix"
            )
        
        if object_key:
            # Delete single object
            success = storage_service.delete_object(object_key)
            
            if success:
                return DeleteResponse(
                    deleted=True,
                    key=object_key,
                    message=f"Successfully deleted {object_key}"
                )
            else:
                return DeleteResponse(
                    deleted=False,
                    key=object_key,
                    message=f"Failed to delete {object_key}"
                )
        
        else:
            # Delete by prefix
            count = storage_service.delete_objects_by_prefix(prefix)
            
            return DeleteResponse(
                deleted=True,
                count=count,
                message=f"Successfully deleted {count} objects with prefix '{prefix}'"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting object(s): {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup-session/{session_id}")
async def cleanup_session(
    session_id: str,
    db: Session = Depends(get_db)
) -> DeleteResponse:
    """
    Delete all objects associated with a specific upload session
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Delete all objects with session prefix
        prefix = f"uploads/{from_db_id(session.id)}/"
        count = storage_service.delete_objects_by_prefix(prefix)
        
        # Update file records
        for file in session.files:
            if file.bucket_key:
                file.bucket_key = None
                file.bucket_url = None
        
        db.commit()
        
        return DeleteResponse(
            deleted=True,
            count=count,
            message=f"Successfully cleaned up {count} objects for session {session_id}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up session: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_storage_stats(
    prefix: Optional[str] = Query(None, description="Prefix to filter statistics")
) -> Dict[str, Any]:
    """
    Get storage statistics
    """
    try:
        # Get all objects
        all_objects = []
        continuation_token = None
        
        while True:
            params = {
                'Bucket': storage_service.bucket_name,
                'MaxKeys': 1000
            }
            
            if prefix:
                params['Prefix'] = prefix
            
            if continuation_token:
                params['ContinuationToken'] = continuation_token
            
            response = storage_service.client.list_objects_v2(**params)
            
            for obj in response.get('Contents', []):
                all_objects.append({
                    'key': obj['Key'],
                    'size': obj['Size']
                })
            
            if not response.get('IsTruncated', False):
                break
            
            continuation_token = response.get('NextContinuationToken')
        
        # Calculate statistics
        total_size = sum(obj['size'] for obj in all_objects)
        
        # Group by prefix (first directory)
        prefix_stats = {}
        for obj in all_objects:
            parts = obj['key'].split('/')
            if len(parts) > 1:
                prefix = parts[0]
                if prefix not in prefix_stats:
                    prefix_stats[prefix] = {'count': 0, 'size': 0}
                prefix_stats[prefix]['count'] += 1
                prefix_stats[prefix]['size'] += obj['size']
        
        return {
            'total_objects': len(all_objects),
            'total_size_mb': total_size / (1024 * 1024),
            'total_size_gb': total_size / (1024 * 1024 * 1024),
            'prefix_breakdown': prefix_stats,
            'bucket_name': storage_service.bucket_name
        }
        
    except Exception as e:
        logger.error(f"Error getting storage stats: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/presigned-url/{object_key:path}")
async def generate_presigned_url(
    object_key: str,
    expiration: int = Query(3600, ge=60, le=86400, description="URL expiration in seconds"),
    download: bool = Query(False, description="Generate download URL instead of view URL")
) -> Dict[str, str]:
    """
    Generate a presigned URL for accessing an object
    """
    try:
        # Check if object exists
        if not storage_service.check_object_exists(object_key):
            raise HTTPException(status_code=404, detail="Object not found")
        
        # Generate presigned URL
        url = storage_service.generate_presigned_url(
            object_key=object_key,
            expiration=expiration,
            http_method="GET"
        )
        
        if not url:
            raise HTTPException(status_code=500, detail="Failed to generate presigned URL")
        
        return {
            'url': url,
            'expires_in_seconds': expiration,
            'object_key': object_key
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned URL: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
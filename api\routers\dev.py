from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from api.models.database import get_db
from api.services.dev_uploader import dev_uploader
from api.services.dev_cache import dev_session_cache
from api.config import settings
from api.utils.logger import logger


# Request/Response models
class ProcessLocalFilesRequest(BaseModel):
    source: Optional[str] = "both"  # "mp_materials", "resido", or "both"
    file_types: Optional[List[str]] = None
    subdirectory: Optional[str] = None
    auto_process: Optional[bool] = True
    user_id: Optional[str] = None


class ProcessSpecificFilesRequest(BaseModel):
    file_paths: List[str]
    auto_process: bool = True
    user_id: Optional[str] = None


class LocalFileInfo(BaseModel):
    full_path: str
    relative_path: str
    filename: str
    size: int
    extension: str
    directory: str
    source: str


# Create router
router = APIRouter(prefix="/dev", tags=["development"])


# Middleware to ensure dev-only access
async def ensure_dev_mode():
    """Ensure the API is running in development mode"""
    if settings.environment != "dev":
        raise HTTPException(
            status_code=403,
            detail="Development endpoints are only available in dev environment"
        )


@router.post("/process", dependencies=[Depends(ensure_dev_mode)])
async def process_local_files(
    request: ProcessLocalFilesRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Process files from local directories (mp_materials/resido) and upload to S3
    
    This endpoint:
    1. Scans the specified local directories
    2. Uploads all found files to DigitalOcean Spaces
    3. Creates a session with all file records
    4. Optionally triggers processing automatically
    """
    try:
        logger.info(f"[/dev/process] Request received - source: {request.source}, auto_process: {request.auto_process}")
        
        result = await dev_uploader.process_local_files(
            db=db,
            source=request.source,
            file_types=request.file_types,
            subdirectory=request.subdirectory,
            auto_process=request.auto_process,
            user_id=request.user_id,
            background_tasks=background_tasks
        )
        
        logger.info(f"[/dev/process] Returning result - session_id: {result.get('session_id')}, cached: {result.get('cached', False)}")
        return result
        
    except ValueError as e:
        logger.error(f"[/dev/process] ValueError: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"[/dev/process] Error processing local files: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list-local-files", dependencies=[Depends(ensure_dev_mode)])
async def list_local_files(
    source: str = Query("both", description="Source directory: mp_materials, resido, or both"),
    file_types: Optional[str] = Query(None, description="Comma-separated file types (pdf,xls,xlsx)"),
    subdirectory: Optional[str] = Query(None, description="Specific subdirectory to scan")
) -> List[LocalFileInfo]:
    """
    List all files that would be processed without actually uploading them
    """
    try:
        # Parse file types
        file_type_list = None
        if file_types:
            file_type_list = [ft.strip() for ft in file_types.split(",")]
        
        # Get files from all requested sources
        all_files = []
        
        if source == "both":
            sources = ["mp_materials", "resido"]
        else:
            sources = [source]
        
        for src in sources:
            files = dev_uploader.scan_directory(src, file_type_list, subdirectory)
            all_files.extend(files)
        
        # Convert to response model
        return [LocalFileInfo(**f) for f in all_files]
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error listing local files: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/process-specific-files", dependencies=[Depends(ensure_dev_mode)])
async def process_specific_files(
    request: ProcessSpecificFilesRequest,
    db: Session = Depends(get_db)
):
    """
    Process specific files by providing their full paths
    """
    try:
        result = await dev_uploader.process_specific_files(
            db=db,
            file_paths=request.file_paths,
            user_id=request.user_id,
            auto_process=request.auto_process
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing specific files: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats", dependencies=[Depends(ensure_dev_mode)])
async def get_dev_stats():
    """
    Get statistics about available local files
    """
    try:
        stats = {}
        
        for source in ["mp_materials", "resido"]:
            files = dev_uploader.scan_directory(source)
            
            # Calculate stats
            pdf_files = [f for f in files if f['extension'] == '.pdf']
            xls_files = [f for f in files if f['extension'] in ['.xls', '.xlsx']]
            
            stats[source] = {
                "total_files": len(files),
                "pdf_files": len(pdf_files),
                "excel_files": len(xls_files),
                "total_size_mb": sum(f['size'] for f in files) / (1024 * 1024),
                "directories": list(set(f['directory'] for f in files))
            }
        
        stats["combined"] = {
            "total_files": sum(s["total_files"] for s in stats.values()),
            "total_size_mb": sum(s["total_size_mb"] for s in stats.values())
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting dev stats: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Cache management endpoints
@router.get("/cache/status", dependencies=[Depends(ensure_dev_mode)])
async def get_cache_status():
    """
    Get current cache status and contents
    """
    try:
        logger.info("[/dev/cache/status] Getting cache status")
        status = dev_session_cache.get_cache_status()
        logger.info(f"[/dev/cache/status] Found {status['total_sessions']} cached sessions")
        return status
    except Exception as e:
        logger.error(f"[/dev/cache/status] Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cache", dependencies=[Depends(ensure_dev_mode)])
async def clear_cache():
    """
    Clear all cached sessions
    """
    try:
        logger.info("[/dev/cache DELETE] Clearing cache")
        result = dev_session_cache.clear_cache()
        logger.info(f"[/dev/cache DELETE] {result['message']}")
        return result
    except Exception as e:
        logger.error(f"[/dev/cache DELETE] Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache/verify", dependencies=[Depends(ensure_dev_mode)])
async def verify_cache(db: Session = Depends(get_db)):
    """
    Verify all cached sessions and clean up invalid ones
    """
    try:
        logger.info("[/dev/cache/verify] Starting cache verification")
        result = dev_session_cache.verify_all_sessions(db)
        logger.info(f"[/dev/cache/verify] {result['message']}")
        return result
    except Exception as e:
        logger.error(f"[/dev/cache/verify] Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
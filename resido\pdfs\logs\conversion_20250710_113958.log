2025-07-10 11:39:58,971 - INFO - Starting HTML to PDF conversion in 'resido' mode
2025-07-10 11:39:59,027 - INFO - Successfully loaded Resido Excel file: DueDiligence docs _ Resido .xlsx
2025-07-10 11:39:59,029 - <PERSON>FO - Extracted 38 rows from Resido file
2025-07-10 11:39:59,029 - INFO - Found 19 HTML documents to process
2025-07-10 11:39:59,029 - INFO - Test mode enabled - processing first document only
2025-07-10 11:39:59,037 - INFO - Processing document 1: Information Statement
2025-07-10 11:39:59,037 - DEBUG - Fetching URL: https://www.sec.gov/Archives/edgar/data/1740332/000095012318009022/filename2.htm
2025-07-10 11:40:00,047 - DEBUG - Starting new HTTPS connection (1): www.sec.gov:443
2025-07-10 11:40:00,126 - DEBUG - https://www.sec.gov:443 "GET /Archives/edgar/data/1740332/000095012318009022/filename2.htm HTTP/1.1" 200 None
2025-07-10 11:40:00,234 - DEBUG - Status: 200, Content length: 2178753
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g54b18.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g18e65.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g59j71.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g65l32.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g69z42.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g86r91.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g63e41.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g80s62.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g22u65.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g83g52.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g35x23.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g17i64.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g28u42.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g71c59.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g26x76.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g96u99.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g17l56.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g61h08.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g09o31.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g85z25.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g15k78.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g72v77.jpg
2025-07-10 11:40:00,767 - DEBUG - Removing broken image reference: g601987g68x62.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g50w99.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g86r91.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g63e41.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g80s62.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g22u65.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g83g52.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g70u16.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g70s69.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g58u54.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g21s77.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g03s84.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g35x23.jpg
2025-07-10 11:40:00,768 - DEBUG - Removing broken image reference: g601987g87t54.jpg
2025-07-10 11:40:01,553 - WARNING - Ignored `border-width:21%` at 1:19, invalid value.
2025-07-10 11:40:01,554 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,555 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,555 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,560 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,560 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,560 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,560 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,562 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,562 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,562 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,562 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,568 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,582 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,582 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,582 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,582 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,582 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,583 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,584 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,584 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,585 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,585 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,585 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,585 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,585 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,585 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,586 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,586 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,587 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,587 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,587 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,587 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,587 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,587 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,588 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,589 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,590 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,590 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,590 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,590 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,591 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,593 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,593 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,593 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,593 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,594 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,595 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,595 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,595 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,595 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,595 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,595 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,595 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,595 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,595 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,595 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,596 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,596 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,596 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,597 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,597 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,598 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,598 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,598 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,598 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,599 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,600 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,600 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,601 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,602 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,604 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,605 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,605 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,606 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,606 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,609 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,609 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,609 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,609 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,610 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,610 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,610 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,611 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,612 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,612 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,612 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,612 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,612 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,613 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,614 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,615 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,616 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,617 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,618 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,619 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,620 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,621 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,622 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,623 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,624 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,625 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,626 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:29.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,626 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,627 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,627 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,627 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,665 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,665 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,665 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,665 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,666 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,667 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,667 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,667 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,668 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,669 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,669 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,669 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,674 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,674 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,674 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,674 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,675 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,675 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,679 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,679 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,680 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,681 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,682 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,683 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,684 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,685 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,686 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,687 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,688 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,690 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,691 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,692 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,693 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,694 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,695 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,696 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,697 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,698 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,699 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,699 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,699 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,700 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,701 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,702 - WARNING - Error: Stop token reached before {} block for a qualified rule. at 1:30.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,702 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,702 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,702 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,702 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,702 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,703 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,704 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,705 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,706 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,707 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,708 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,709 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,710 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,711 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,711 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,711 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,712 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,713 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,714 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,715 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,716 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,717 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Ignored `vertical-align:` at 1:16, no value.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,718 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Ignored `border-width:10%` at 1:19, invalid value.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,719 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,720 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,721 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,722 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,723 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,724 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,725 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,726 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,726 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,727 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,728 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,729 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,730 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,731 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,732 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,733 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,734 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,735 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,736 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,737 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,738 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:1.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,739 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:01,740 - WARNING - Error: EOF reached before {} block for a qualified rule. at 1:4.
2025-07-10 11:40:04,888 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:04,992 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,009 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:05,010 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:05,581 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,584 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,585 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,637 - WARNING - This table row has more columns than the table, ignored 29 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,642 - WARNING - This table row has more columns than the table, ignored 11 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,647 - WARNING - This table row has more columns than the table, ignored 41 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,774 - WARNING - This table row has more columns than the table, ignored 19 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,778 - WARNING - This table row has more columns than the table, ignored 7 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,779 - WARNING - This table row has more columns than the table, ignored 25 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,811 - WARNING - This table row has more columns than the table, ignored 21 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,816 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,926 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:05,939 - WARNING - This table row has more columns than the table, ignored 21 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,945 - WARNING - This table row has more columns than the table, ignored 21 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:05,950 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,043 - WARNING - This table row has more columns than the table, ignored 27 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,114 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,180 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,231 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,284 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,399 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,400 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,401 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,407 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,416 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,417 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,431 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,439 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,446 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,449 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,518 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,564 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,582 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,583 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:06,712 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,729 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,743 - WARNING - This table row has more columns than the table, ignored 20 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,744 - WARNING - This table row has more columns than the table, ignored 19 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,746 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,753 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,787 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,790 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,790 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,798 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,811 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,814 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,814 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,830 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,832 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,833 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,842 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,845 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,845 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,852 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,854 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,855 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,871 - WARNING - This table row has more columns than the table, ignored 18 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,874 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,913 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,914 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,917 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,937 - WARNING - This table row has more columns than the table, ignored 18 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,940 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,978 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,980 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:06,983 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,002 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,002 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,008 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,022 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,023 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,033 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,034 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,035 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,045 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,046 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,052 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,053 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,067 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,098 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,101 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,129 - WARNING - This table row has more columns than the table, ignored 7 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,131 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,134 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,140 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,143 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,146 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,153 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,158 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,162 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,194 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,195 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,196 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,213 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,215 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,216 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,239 - WARNING - This table row has more columns than the table, ignored 11 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,241 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,242 - WARNING - This table row has more columns than the table, ignored 17 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,290 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,367 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,368 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,368 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,369 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,372 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,373 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,417 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,431 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,565 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,572 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,590 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,592 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,592 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,627 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,629 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,630 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,653 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,654 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,655 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,690 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,704 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,706 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,707 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,813 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,814 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,902 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,904 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,916 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,917 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,931 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,933 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,960 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,971 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:07,992 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:07,994 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,011 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,012 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,031 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,085 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,087 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,102 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,104 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,118 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,119 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,151 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,153 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,184 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,185 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,243 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,254 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,280 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,910 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,911 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,923 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,924 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,934 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,935 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,961 - WARNING - This table row has more columns than the table, ignored 7 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,988 - WARNING - This table row has more columns than the table, ignored 18 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:08,992 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,025 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,026 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,044 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:09,058 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,059 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,063 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,065 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,081 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,123 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,166 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,169 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,198 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,200 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,214 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,215 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,230 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,231 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,253 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,255 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,303 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,305 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,315 - WARNING - This table row has more columns than the table, ignored 11 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,316 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,318 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,333 - WARNING - This table row has more columns than the table, ignored 9 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,335 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,356 - WARNING - This table row has more columns than the table, ignored 18 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,360 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,394 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,396 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,397 - WARNING - This table row has more columns than the table, ignored 13 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,447 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,449 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,478 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,479 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,491 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,513 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,515 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,531 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,532 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,533 - WARNING - This table row has more columns than the table, ignored 5 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,630 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,641 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,654 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,656 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,693 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,695 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,713 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,715 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,738 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,775 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,782 - WARNING - This table row has more columns than the table, ignored 6 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,800 - WARNING - This table row has more columns than the table, ignored 1 cells: [<TableCellBox td>]
2025-07-10 11:40:09,813 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,831 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,842 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,863 - WARNING - This table row has more columns than the table, ignored 3 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,916 - WARNING - This table row has more columns than the table, ignored 2 cells: [<TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,942 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,944 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,968 - WARNING - This table row has more columns than the table, ignored 14 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,969 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,971 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,991 - WARNING - This table row has more columns than the table, ignored 10 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:09,993 - WARNING - This table row has more columns than the table, ignored 4 cells: [<TableCellBox td>, <TableCellBox td>, <TableCellBox td>, <TableCellBox td>]
2025-07-10 11:40:12,829 - DEBUG - Successfully created PDF: resido/pdfs/information-statement.pdf
2025-07-10 11:40:12,829 - INFO - ✓ Successfully converted: information-statement.pdf

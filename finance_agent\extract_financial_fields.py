import asyncio
import json
import os
from dotenv import load_dotenv
import openai
from llm_client import extract_fields_from_page

# Load API keys from .env
load_dotenv()
agentic_api_key = os.getenv("AGENTIC_API_KEY")
agentic_base_url = os.getenv("AGENTIC_BASE_URL")

REDUCTION_PROMPT = """
You are an expert financial analyst with exceptional storytelling capabilities. You will receive multiple page results from the same financial document that have been processed individually. Your task is to combine and synthesize these results into a comprehensive, coherent analysis that blends quantitative insights with narrative storytelling.

Structure your response as follows:

## Executive Summary
Provide a concise 3-4 sentence executive summary that captures:
- The most critical financial insights and key metrics
- Overall business performance trajectory (growth, decline, stability)
- Key strategic implications or recommendations
- Primary risks or opportunities identified

## Financial Analysis & Insights
Create a narrative-driven analysis that:
1. Combines all relevant information from different pages into a flowing story
2. Resolves any contradictions by using the most recent or complete information (note discrepancies when relevant)
3. Maintains all specific facts, figures, dates, and quantitative data
4. Presents data in both tabular format AND narrative context
5. Explains the "why" behind the numbers and their business implications
6. Identifies trends, patterns, and key relationships between different metrics
7. Provides context about what these findings mean for the business/industry
8. **Follows chronological flow** when dealing with time-series data (quarterly results, year-over-year changes)

Guidelines for Enhanced Reporting:
- **AGGRESSIVE FACT-CHECKING**: Be extraordinarily careful with ALL facts, numbers, percentages, dates, and monetary values - verify every figure against source pages
- **ZERO TOLERANCE FOR INACCURACY**: Never approximate, round, or estimate numbers - use EXACT figures as stated in the source pages
- **EXPLICIT MENTION ONLY**: Only include information that is explicitly and clearly stated in the individual page results - no assumptions or inferences
- **Narrative Flow**: Create a logical story that connects different data points across pages
- **Financial Storytelling**: Explain what the numbers reveal about business performance, strategy, and outlook
- **Context & Implications**: Discuss what the findings mean in the broader business context
- **Table Integration**: Use well-formatted tables for precise data presentation, then immediately narrate their significance and implications
- **Document Coherence**: Understand and reflect the overall context of the entire document
- **Trend Analysis**: Identify and explain patterns across different time periods or categories
- **Business Impact**: Translate financial metrics into business insights and strategic implications
- **Chronological Structure**: Organize information chronologically for clarity
- **Contradiction Resolution**: When information conflicts across pages, use the most recent/complete data and note the discrepancy
- **Quantitative Balance**: Balance detailed numbers with high-level insights - don't just list figures, explain their meaning
- **Eliminate redundancy** while preserving all unique insights
- **CRITICAL**: Include page number citations in the format [Page X] after each piece of information to show its source
- When combining information from multiple pages, cite all relevant page numbers
- If information spans multiple pages, present it in a unified way with comprehensive citations

Query: {query}

Individual Page Results:
{page_results}

Provide a comprehensive financial analysis that combines quantitative precision with narrative storytelling, including proper page citations [Page X]:
"""

def split_markdown_into_pages(md_text):
    """Split markdown text into pages and return list of (page_number, page_content) tuples"""
    pages = [page.strip() for page in md_text.split('================================================================================') if page.strip()]
    # Return tuples of (page_number, page_content) where page_number is 1-indexed
    return [(i + 1, page) for i, page in enumerate(pages)]

def log_processing_summary(valid_results, total_pages):
    """Log a summary of all page processing results"""
    print(f"\n{'='*80}")
    print(f"📊 PROCESSING SUMMARY")
    print(f"{'='*80}")
    
    successful_pages = [page_num for page_num, result in valid_results if result != "Not Found"]
    failed_pages = [page_num for page_num, result in valid_results if result == "Not Found"]
    
    print(f"📄 Total Pages: {total_pages}")
    print(f"✅ Successfully Processed: {len(successful_pages)} pages")
    print(f"❌ No Information Found: {len(failed_pages)} pages")
    
    if successful_pages:
        print(f"🎯 Pages with Results: {', '.join(map(str, successful_pages))}")
    
    if failed_pages:
        print(f"⚠️  Pages without Results: {', '.join(map(str, failed_pages))}")
    
    # Calculate total content length
    total_content_length = sum(len(result) for _, result in valid_results if result != "Not Found")
    print(f"📝 Total Content Extracted: {total_content_length} characters")
    
    print(f"{'='*80}\n")

async def reduce_results_by_document(page_results, query):
    """
    Use LLM to intelligently combine results from multiple pages of the same document
    page_results should be a list of tuples (page_number, result)
    """
    if not page_results:
        return "Not Found"
    
    # Filter out "Not Found" results
    valid_results = [(page_num, result) for page_num, result in page_results if result != "Not Found"]
    
    if not valid_results:
        return "Not Found"
    
    # If only one valid result, return it directly
    if len(valid_results) == 1:
        print(f"📄 Only one page with results (Page {valid_results[0][0]}), returning directly...")
        return valid_results[0][1]  # Return just the result content
    
    print(f"🔄 Combining {len(valid_results)} page results using LLM...")
    print(f"📑 Pages to combine: {', '.join([str(page_num) for page_num, _ in valid_results])}")
    
    # Configure OpenAI client
    client = openai.AsyncOpenAI(
        api_key=agentic_api_key,
        base_url=agentic_base_url,
        max_retries=3,
        timeout=60.0
    )
    
    # Prepare the page results for the prompt with page numbers
    page_results_text = "\n\n---\n\n".join([f"Page {page_num} Results:\n{result}" for page_num, result in valid_results])
    
    prompt = REDUCTION_PROMPT.format(query=query, page_results=page_results_text)
    
    try:
        print(f"🤖 Calling LLM for result combination...")
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are an expert financial analyst with exceptional storytelling capabilities. Create comprehensive financial analyses that blend quantitative precision with narrative storytelling, including executive summaries, chronological flow, and strategic insights with proper page citations."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2  # Very low temperature for consistent combination
        )
        
        combined_result = response.choices[0].message.content.strip()
        print(f"✅ Successfully combined results into {len(combined_result)} characters")
        
        # Log combination summary
        print(f"\n{'='*60}")
        print(f"🎯 COMBINATION COMPLETE")
        print(f"{'='*60}")
        print(f"📄 Combined {len(valid_results)} pages into unified result")
        print(f"📝 Final Result Length: {len(combined_result)} characters")
        print(f"{'='*60}\n")
        
        return combined_result
        
    except Exception as e:
        print(f"❌ Error combining results: {e}")
        print(f"🔄 Using fallback: concatenating all results with page numbers")
        # Fallback: concatenate all results with page numbers
        return "\n\n---\n\n".join([f"**Page {page_num}:**\n{result}" for page_num, result in valid_results])

async def main():
    print(f"\n{'='*80}")
    print(f"🚀 FINANCIAL FIELD EXTRACTION STARTED")
    print(f"{'='*80}")
    
    # Load document
    print("📁 Loading markdown document...")
    with open('output/2018_10K.md', 'r', encoding='utf-8') as f:
        md_text = f.read()
    print(f"✅ Loaded document with {len(md_text):,} characters")
    
    # Split into pages
    print("📄 Splitting document into pages...")
    pages_with_numbers = split_markdown_into_pages(md_text)
    print(f"✅ Document split into {len(pages_with_numbers)} pages")
    
    if len(pages_with_numbers) == 0:
        print("❌ No pages found to process!")
        return
    
    # Get query from user
    print(f"\n{'='*60}")
    print("🔍 QUERY SETUP")
    print(f"{'='*60}")
    query = input("Enter your query (e.g., 'key financial metrics', 'risk factors', 'revenue growth'): ")
    print(f"✅ Query set: '{query}'")
    
    # Start processing
    print(f"\n{'='*80}")
    print(f"⚙️  PROCESSING {len(pages_with_numbers)} PAGES IN PARALLEL")
    print(f"{'='*80}")
    print(f"🔍 Query: {query}")
    print(f"🔄 Concurrency Limit: 5 simultaneous requests")
    print(f"🎯 Target: Extract relevant information from each page")
    
    # Create semaphore to control concurrent API calls (limit to 5 concurrent requests)
    semaphore = asyncio.Semaphore(50)
    
    async def process_with_semaphore(page_num, page_content):
        async with semaphore:
            result = await extract_fields_from_page(page_content, query, page_num)
            return (page_num, result)
    
    # Process pages concurrently using asyncio with controlled concurrency
    start_time = asyncio.get_event_loop().time()
    tasks = [process_with_semaphore(page_num, page_content) for page_num, page_content in pages_with_numbers]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = asyncio.get_event_loop().time()
    
    # Filter out any exceptions
    valid_results = [r for r in results if not isinstance(r, Exception)]
    
    # Log processing summary
    log_processing_summary(valid_results, len(pages_with_numbers))
    
    print(f"⏱️  Total Processing Time: {end_time - start_time:.2f} seconds")
    print(f"📊 Average Time per Page: {(end_time - start_time) / len(pages_with_numbers):.2f} seconds")
    
    # Combine results
    print(f"\n{'='*80}")
    print(f"🔄 COMBINING RESULTS INTO FINAL SUMMARY")
    print(f"{'='*80}")
    
    combined_result = await reduce_results_by_document(valid_results, query)
    
    # Save results
    print(f"💾 Saving results to 'extracted_results.md'...")
    with open('extracted_results.md', 'w') as f:
        f.write(f"# Query: {query}\n\n")
        f.write(combined_result)
    
    print(f"\n{'='*80}")
    print(f"🎉 EXTRACTION COMPLETE!")
    print(f"{'='*80}")
    print(f"📄 Results saved to: extracted_results.md")
    print(f"🔍 Query processed: {query}")
    print(f"📊 Final result length: {len(combined_result)} characters")
    print(f"{'='*80}\n")

if __name__ == '__main__':
    asyncio.run(main()) 

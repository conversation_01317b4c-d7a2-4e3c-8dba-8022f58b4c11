"""
<PERSON><PERSON>t to reset categorization data for testing
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger


def reset_categorization_data():
    """Reset all categorization data to allow re-processing"""
    
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            # Reset uploaded_files categorization data
            reset_files_sql = """
            UPDATE uploaded_files
            SET 
                due_diligence_category = 'unknown'::duediligencecategory,
                categorization_confidence = 0.0,
                categorization_metadata = NULL,
                categorized_at = NULL,
                categorization_status = 'pending'::categorizationstatus,
                categorization_started_at = NULL,
                agent_insights = NULL
            WHERE bucket_key IS NOT NULL;
            """
            
            result = conn.execute(text(reset_files_sql))
            files_reset = result.rowcount
            
            # Reset upload_sessions categorization tracking
            reset_sessions_sql = """
            UPDATE upload_sessions
            SET
                categorization_total = 0,
                categorization_completed = 0,
                categorization_failed = 0,
                categorization_started_at = NULL,
                categorization_completed_at = NULL;
            """
            
            result = conn.execute(text(reset_sessions_sql))
            sessions_reset = result.rowcount
            
            conn.commit()
            
            print(f"✅ Successfully reset categorization data:")
            print(f"   - Reset {files_reset} files to pending categorization")
            print(f"   - Reset {sessions_reset} sessions")
            
            logger.info(f"Reset categorization data: {files_reset} files, {sessions_reset} sessions")
            
    except Exception as e:
        logger.error(f"Error resetting categorization data: {str(e)}", exc_info=True)
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    finally:
        engine.dispose()


if __name__ == "__main__":
    print("Resetting categorization data...")
    reset_categorization_data()
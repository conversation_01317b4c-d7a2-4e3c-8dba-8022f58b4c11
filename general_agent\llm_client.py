import os
import time
import openai
from dotenv import load_dotenv
from prompts import GPA_EXTRACTION_PROMPT 

load_dotenv()
agentic_api_key = os.getenv("AGENTIC_API_KEY")
agentic_base_url = os.getenv("AGENTIC_BASE_URL")


async def extract_info_from_page(page_text, query, page_number):
    """This function is the 'Map' step, processing one page at a time."""
    client = openai.AsyncOpenAI(
        api_key=agentic_api_key,
        base_url=agentic_base_url,
        max_retries=3,
        timeout=60.0
    )

    prompt = GPA_EXTRACTION_PROMPT.format(query=query, page_number=page_number)
    
    try:
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": f"You are an expert research analyst. You are currently analyzing Page {page_number}."},
                {"role": "user", "content": prompt + page_text}
            ],
            max_tokens=4000,
            temperature=0.2
        )
        result = response.choices[0].message.content.strip()
        print(f"✅ Processed Page {page_number}. Result: {'Found' if result != 'Not Found' else 'Not Found'}")
        return result
    except Exception as e:
        print(f"❌ Error processing page {page_number}: {e}")
        return "Not Found"

"""
Migration script to add due diligence categorization columns to uploaded_files table
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger

def run_migration():
    """Run the migration to add due diligence columns"""
    
    # Create engine
    engine = create_engine(settings.database_url)
    
    # SQL statements to add the new columns
    migration_steps = [
        # Step 1: Drop existing enum type and create new one
        """
        DROP TYPE IF EXISTS duediligencecategory CASCADE;
        CREATE TYPE duediligencecategory AS ENUM (
            'financial_due_diligence',
            'technical_due_diligence', 
            'legal_due_diligence',
            'operational_due_diligence',
            'market_due_diligence',
            'unknown'
        );
        """,
        
        # Step 2: Add due diligence categorization columns
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS due_diligence_category duediligencecategory DEFAULT 'unknown'::duediligencecategory;
        """,
        
        # Step 3: Add other columns
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS categorization_confidence DECIMAL(3,2) DEFAULT 0.0;
        """,
        
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS categorization_metadata JSONB;
        """,
        
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS categorized_at TIMESTAMP WITH TIME ZONE;
        """,
        
        # Step 4: Create indexes
        """
        CREATE INDEX IF NOT EXISTS idx_files_dd_category 
        ON uploaded_files(due_diligence_category, categorized_at);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_files_session_category 
        ON uploaded_files(session_id, due_diligence_category);
        """
    ]
    
    try:
        with engine.connect() as conn:
            # Execute migration steps one by one
            for i, step in enumerate(migration_steps, 1):
                print(f"Executing step {i}/{len(migration_steps)}...")
                conn.execute(text(step))
                conn.commit()
            
        logger.info("Successfully added due diligence categorization columns")
        print("✅ Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}", exc_info=True)
        print(f"❌ Migration failed: {str(e)}")
        sys.exit(1)
    
    finally:
        engine.dispose()

if __name__ == "__main__":
    print("Running migration to add due diligence categorization columns...")
    run_migration()
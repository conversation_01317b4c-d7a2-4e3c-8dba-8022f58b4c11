from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum


class UploadStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class FileStatus(str, Enum):
    PENDING = "pending"
    UPLOADED = "uploaded"
    PROCESSED = "processed"
    FAILED = "failed"


# Request schemas
class PrepareUploadRequest(BaseModel):
    filenames: List[str] = Field(..., min_items=1, max_items=1000)
    
    class Config:
        json_schema_extra = {
            "example": {
                "filenames": ["document1.pdf", "document2.pdf", "report.xlsx"]
            }
        }


class FileMapping(BaseModel):
    filename: str
    bucket_id: str
    file_size: Optional[int] = None
    content_type: Optional[str] = None


class CompleteUploadRequest(BaseModel):
    session_id: str
    files: List[FileMapping]
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "files": [
                    {
                        "filename": "document1.pdf",
                        "bucket_id": "uploads/123e4567/document1.pdf",
                        "file_size": 1024000,
                        "content_type": "application/pdf"
                    }
                ]
            }
        }


# Response schemas
class PresignedUrlMapping(BaseModel):
    filename: str
    upload_url: str
    file_id: str
    bucket_key: str


class PrepareUploadResponse(BaseModel):
    session_id: str
    presigned_urls: List[PresignedUrlMapping]
    expires_in_seconds: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "presigned_urls": [
                    {
                        "filename": "document1.pdf",
                        "upload_url": "https://spaces.digitalocean.com/...",
                        "file_id": "file_123",
                        "bucket_key": "uploads/123e4567/document1.pdf"
                    }
                ],
                "expires_in_seconds": 900
            }
        }


class FileInfo(BaseModel):
    id: str
    filename: str
    status: FileStatus
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    created_at: datetime
    error_message: Optional[str] = None


class SessionStatusResponse(BaseModel):
    session_id: str
    status: UploadStatus
    total_files: int
    processed_files: int
    created_at: datetime
    updated_at: datetime
    files: List[FileInfo]
    error_message: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "processing",
                "total_files": 3,
                "processed_files": 1,
                "created_at": "2024-01-14T10:00:00Z",
                "updated_at": "2024-01-14T10:05:00Z",
                "files": [
                    {
                        "id": "file_123",
                        "filename": "document1.pdf",
                        "status": "processed",
                        "file_size": 1024000,
                        "content_type": "application/pdf",
                        "created_at": "2024-01-14T10:00:00Z"
                    }
                ]
            }
        }


class ProcessResponse(BaseModel):
    session_id: str
    status: str
    message: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "processing",
                "message": "Processing started for 3 files"
            }
        }


class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "detail": "Session not found",
                "error_code": "SESSION_NOT_FOUND"
            }
        }
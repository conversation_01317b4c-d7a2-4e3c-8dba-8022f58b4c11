"""
Data migration to update existing due diligence category values to match new enum
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger

def run_data_migration():
    """Update existing data to match new enum values"""
    
    # Create engine
    engine = create_engine(settings.database_url)
    
    # Check what values exist in the database
    check_sql = """
    SELECT DISTINCT due_diligence_category, COUNT(*) as count
    FROM uploaded_files 
    WHERE due_diligence_category IS NOT NULL
    GROUP BY due_diligence_category;
    """
    
    # Update existing data to match new enum values
    update_sql = """
    UPDATE uploaded_files 
    SET due_diligence_category = 'unknown'::duediligencecategory
    WHERE due_diligence_category IS NULL;
    """
    
    try:
        with engine.connect() as conn:
            # Check existing values
            result = conn.execute(text(check_sql))
            existing_values = result.fetchall()
            
            print("Existing due diligence category values:")
            for row in existing_values:
                print(f"  {row[0]}: {row[1]} records")
            
            # Update NULL values to 'unknown'
            result = conn.execute(text(update_sql))
            updated_rows = result.rowcount
            conn.commit()
            
            print(f"Updated {updated_rows} NULL values to 'unknown'")
            
        logger.info("Successfully updated existing data for due diligence categories")
        print("✅ Data migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Data migration failed: {str(e)}", exc_info=True)
        print(f"❌ Data migration failed: {str(e)}")
        sys.exit(1)
    
    finally:
        engine.dispose()

if __name__ == "__main__":
    print("Running data migration for due diligence categories...")
    run_data_migration()
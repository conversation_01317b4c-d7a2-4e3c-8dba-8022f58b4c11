import logging
import os
from typing import List, Dict, Optional, Any

try:
    from sec_api import Query<PERSON>pi, RenderApi, Extractor<PERSON><PERSON>
except ImportError:
    print("sec_api package not installed. Please run: pip install sec-api")
    QueryApi = None
    RenderApi = None
    ExtractorApi = None

logger = logging.getLogger(__name__)


class SECFilingAgent:
    """
    SEC Filing API agent that provides access to SEC EDGAR database.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the SEC Filing Agent.
        
        Args:
            api_key: SEC API key. If not provided, will try to get from environment.
        """
        self.api_key = api_key or os.getenv("SEC_API_KEY")
        if not self.api_key:
            logger.warning("SEC API key not provided. Some features may be limited.")
        
        # Initialize SEC API clients
        if QueryApi and self.api_key:
            self.query_api = QueryApi(api_key=self.api_key)
            self.render_api = RenderApi(api_key=self.api_key)
            self.extractor_api = ExtractorApi(api_key=self.api_key)
        else:
            self.query_api = None
            self.render_api = None
            self.extractor_api = None
    
    def search_filings(
        self,
        query: str,
        ticker: Optional[str] = None,
        form_type: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search SEC filings based on query and filters.
        
        Args:
            query: Search query
            ticker: Company ticker symbol (e.g., "AAPL")
            form_type: SEC form type (e.g., "10-K", "10-Q", "8-K")
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            limit: Maximum number of results to return
            
        Returns:
            List of filing information dictionaries
        """
        if not self.query_api:
            logger.error("SEC API not properly initialized")
            return []
        
        try:
            # Simplified approach - start with ticker if provided, otherwise use general search
            if ticker:
                # If ticker is provided, focus on that company
                final_query = f"ticker:{ticker.upper()}"
                
                # Add form type if specified
                if form_type:
                    final_query += f" AND formType:{form_type}"
                    
                # Add text search within that company's filings
                if query and query.lower() not in ["*", "all"]:
                    final_query += f" AND {query}"
            else:
                # General search without ticker
                if query and query.lower() not in ["*", "all"]:
                    final_query = query
                else:
                    final_query = "*"
                    
                # Add form type if specified
                if form_type:
                    if final_query == "*":
                        final_query = f"formType:{form_type}"
                    else:
                        final_query += f" AND formType:{form_type}"
            
            # Add date range if provided
            if start_date and end_date:
                final_query += f" AND filedAt:[{start_date} TO {end_date}]"
            elif start_date:
                final_query += f" AND filedAt:[{start_date} TO *]"
            elif end_date:
                final_query += f" AND filedAt:[* TO {end_date}]"
            
            search_query = {
                "query": {"query_string": {"query": final_query}},
                "from": "0", 
                "size": str(limit),
                "sort": [{"filedAt": {"order": "desc"}}]
            }
            
            logger.info(f"Searching SEC filings with final query: {final_query}")
            logger.debug(f"Full search query structure: {search_query}")
            
            response = self.query_api.get_filings(search_query)
            logger.debug(f"API response keys: {response.keys() if isinstance(response, dict) else 'Not a dict'}")
            
            if isinstance(response, dict) and "filings" in response:
                logger.info(f"Raw API returned {len(response['filings'])} filings")
            else:
                logger.warning(f"Unexpected API response format: {type(response)}")
                logger.debug(f"Full response: {response}")
            
            # Process and format results
            results = []
            if isinstance(response, dict) and "filings" in response:
                for filing in response["filings"]:
                    result = {
                        "accession_number": filing.get("accessionNo"),
                        "ticker": filing.get("ticker"),
                        "company_name": filing.get("companyName"),
                        "form_type": filing.get("formType"),
                        "filed_date": filing.get("filedAt"),
                        "period_end_date": filing.get("periodOfReport"),
                        "filing_url": filing.get("linkToFilingDetails"),
                        "description": filing.get("description", ""),
                    }
                    results.append(result)
            else:
                logger.error(f"No 'filings' key in response or response is not a dict")
            
            logger.info(f"Found {len(results)} SEC filings after processing")
            return results
            
        except Exception as e:
            logger.error(f"Error searching SEC filings: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    
    def get_filing_content(self, accession_number: str, ticker: str) -> Optional[str]:
        """
        Get the content of a specific SEC filing.
        
        Args:
            accession_number: SEC accession number
            ticker: Company ticker symbol
            
        Returns:
            Filing content as text or None if failed
        """
        if not self.extractor_api:
            logger.error("SEC Extractor API not properly initialized")
            return None
        
        try:
            logger.info(f"Extracting content for filing {accession_number}")
            
            # Extract text from the filing
            filing_url = f"https://www.sec.gov/Archives/edgar/data/{ticker}/{accession_number.replace('-', '')}/{accession_number}.txt"
            
            # Use the extractor API to get clean text
            response = self.extractor_api.get_section(
                filing_url=filing_url,
                section="1A",
                return_type="text"
            )
            
            return response.get("text", "")
            
        except Exception as e:
            logger.error(f"Error extracting filing content: {str(e)}")
            return None
    
    def search_company_filings(
        self,
        ticker: str,
        form_types: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Search for all recent filings of a specific company.
        
        Args:
            ticker: Company ticker symbol
            form_types: List of form types to filter by (e.g., ["10-K", "10-Q"])
            limit: Maximum number of results
            
        Returns:
            List of company filings
        """
        try:
            query = f"ticker:{ticker.upper()}"
            
            if form_types:
                form_query = " OR ".join([f"formType:{ft}" for ft in form_types])
                query += f" AND ({form_query})"
            
            return self.search_filings(
                query=query,
                ticker=ticker,
                limit=limit
            )
            
        except Exception as e:
            logger.error(f"Error searching company filings: {str(e)}")
            return []


# Global instance for easy access
sec_agent = SECFilingAgent()


def search_sec_filings(
    query: str,
    ticker: Optional[str] = None,
    form_type: Optional[str] = None,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Convenience function to search SEC filings.
    
    Args:
        query: Search query
        ticker: Optional company ticker
        form_type: Optional SEC form type
        limit: Maximum number of results
        
    Returns:
        List of SEC filing results
    """
    return sec_agent.search_filings(
        query=query,
        ticker=ticker,
        form_type=form_type,
        limit=limit
    )

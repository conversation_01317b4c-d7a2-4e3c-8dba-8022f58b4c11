from sqlalchemy import create_engine, <PERSON>umn, String, Integer, BigInteger, DateTime, Enum, ForeignKey, Text, Boolean, Numeric, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.sql import func
from sqlalchemy.pool import NullPool, QueuePool
import enum
import uuid as uuid_lib
from datetime import datetime
from typing import Generator, Optional, Union

from api.config import settings

# Create base class
Base = declarative_base()

# Determine if we're using PostgreSQL
is_postgres = "postgresql" in settings.database_url

# Helper function to convert string IDs to UUID objects for PostgreSQL
def to_db_id(id_value: str):
    """Convert string ID to appropriate database type"""
    if is_postgres and isinstance(id_value, str):
        return uuid.UUID(id_value)
    return id_value

def from_db_id(id_value):
    """Convert database ID to string for consistent API responses"""
    if id_value is None:
        return None
    return str(id_value)

# Create engine with appropriate settings
if is_postgres:
    engine = create_engine(
        settings.database_url,
        poolclass=QueuePool,
        pool_size=settings.database_pool_size,
        max_overflow=settings.database_max_overflow,
        pool_timeout=settings.database_pool_timeout,
        pool_pre_ping=True,  # Verify connections before using
        echo=False
    )
else:
    # SQLite for development
    engine = create_engine(
        settings.database_url,
        connect_args={"check_same_thread": False},
        poolclass=NullPool
    )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# Helper functions for UUID conversion
def to_db_id(id_value: Union[str, uuid_lib.UUID]) -> Union[str, uuid_lib.UUID]:
    """Convert ID to proper database type"""
    if id_value is None:
        return None
    
    if is_postgres:
        # PostgreSQL needs UUID objects
        if isinstance(id_value, str):
            return uuid_lib.UUID(id_value)
        return id_value
    else:
        # SQLite needs strings
        if isinstance(id_value, uuid_lib.UUID):
            return str(id_value)
        return id_value


def from_db_id(id_value: Union[str, uuid_lib.UUID]) -> str:
    """Convert database ID to string for API responses"""
    if id_value is None:
        return None
    
    if isinstance(id_value, uuid_lib.UUID):
        return str(id_value)
    return id_value


class UploadStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class FileStatus(str, enum.Enum):
    PENDING = "pending"
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"


class DueDiligenceCategory(str, enum.Enum):
    FINANCIAL = "financial_due_diligence"
    TECHNICAL = "technical_due_diligence"
    LEGAL = "legal_due_diligence"
    OPERATIONAL = "operational_due_diligence"
    MARKET = "market_due_diligence"
    UNKNOWN = "unknown"


class CategorizationStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class LatticeStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class UploadSession(Base):
    __tablename__ = "upload_sessions"
    
    id = Column(String if not is_postgres else UUID(as_uuid=True), 
                primary_key=True, 
                default=lambda: uuid_lib.uuid4() if is_postgres else str(uuid_lib.uuid4()))
    user_id = Column(String, nullable=True, index=True)
    company_name = Column(String, nullable=True, index=True)
    session_type = Column(String, nullable=True)  # financial, legal, general
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    status = Column(Enum(UploadStatus), default=UploadStatus.PENDING, index=True)
    total_files = Column(Integer, default=0)
    processed_files = Column(Integer, default=0)
    total_size_bytes = Column(BigInteger, default=0)
    session_metadata = Column(JSONB if is_postgres else Text, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Categorization tracking fields
    categorization_total = Column(Integer, default=0)
    categorization_completed = Column(Integer, default=0)
    categorization_failed = Column(Integer, default=0)
    categorization_started_at = Column(DateTime(timezone=True), nullable=True)
    categorization_completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lattice analysis tracking fields
    lattice_status = Column(
        Enum(LatticeStatus, values_callable=lambda obj: [e.value for e in obj]),
        default=LatticeStatus.PENDING,
        index=True
    )
    lattice_started_at = Column(DateTime(timezone=True), nullable=True)
    lattice_completed_at = Column(DateTime(timezone=True), nullable=True)
    lattice_progress = Column(JSONB if is_postgres else Text, nullable=True)  # Real-time progress tracking
    lattice_error_message = Column(Text, nullable=True)
    
    # Relationships
    files = relationship("UploadedFile", back_populates="session", cascade="all, delete-orphan")
    lattice_headers = relationship("LatticeHeader", back_populates="session", cascade="all, delete-orphan")
    lattice_results = relationship("LatticeResult", back_populates="session", cascade="all, delete-orphan")


class UploadedFile(Base):
    __tablename__ = "uploaded_files"
    
    id = Column(String if not is_postgres else UUID(as_uuid=True), 
                primary_key=True, 
                default=lambda: uuid_lib.uuid4() if is_postgres else str(uuid_lib.uuid4()))
    session_id = Column(String if not is_postgres else UUID(as_uuid=True), 
                       ForeignKey("upload_sessions.id", ondelete="CASCADE"), 
                       index=True)
    filename = Column(String, nullable=False)
    bucket_key = Column(String, nullable=True, unique=True)
    bucket_url = Column(String, nullable=True)
    file_size = Column(BigInteger, nullable=True)
    content_type = Column(String, nullable=True)
    file_hash = Column(String, nullable=True, index=True)  # For deduplication
    status = Column(Enum(FileStatus), default=FileStatus.PENDING, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    file_metadata = Column(JSONB if is_postgres else Text, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Due Diligence Categorization Fields
    due_diligence_category = Column(
        Enum(DueDiligenceCategory, values_callable=lambda obj: [e.value for e in obj]), 
        default=DueDiligenceCategory.UNKNOWN, 
        index=True
    )
    categorization_confidence = Column(Numeric(3, 2), default=0.0)  # 0.00 to 1.00
    categorization_metadata = Column(JSONB if is_postgres else Text, nullable=True)
    categorized_at = Column(DateTime(timezone=True), nullable=True)
    
    # Categorization status tracking
    categorization_status = Column(
        Enum(CategorizationStatus, values_callable=lambda obj: [e.value for e in obj]), 
        default=CategorizationStatus.PENDING, 
        index=True
    )
    categorization_started_at = Column(DateTime(timezone=True), nullable=True)
    agent_insights = Column(JSONB if is_postgres else Text, nullable=True)
    
    # Relationships
    session = relationship("UploadSession", back_populates="files")
    processing_results = relationship("FileProcessingResult", back_populates="file", cascade="all, delete-orphan")
    lattice_results = relationship("LatticeResult", back_populates="file", cascade="all, delete-orphan")


class FileProcessingResult(Base):
    __tablename__ = "file_processing_results"
    
    id = Column(String if not is_postgres else UUID(as_uuid=True), 
                primary_key=True, 
                default=lambda: uuid_lib.uuid4() if is_postgres else str(uuid_lib.uuid4()))
    file_id = Column(String if not is_postgres else UUID(as_uuid=True), 
                    ForeignKey("uploaded_files.id", ondelete="CASCADE"), 
                    index=True)
    processing_type = Column(String, nullable=False)  # text_extraction, ai_analysis, etc.
    extracted_text = Column(Text, nullable=True)
    ai_summary = Column(Text, nullable=True)
    entities = Column(JSONB if is_postgres else Text, nullable=True)  # Names, dates, amounts
    classifications = Column(JSONB if is_postgres else Text, nullable=True)  # Document types
    embeddings_generated = Column(Boolean, default=False)
    qdrant_point_ids = Column(JSONB if is_postgres else Text, nullable=True)
    processing_metadata = Column(JSONB if is_postgres else Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    file = relationship("UploadedFile", back_populates="processing_results")


class LatticeHeader(Base):
    """Store lattice headers for sessions - both general and category-specific"""
    __tablename__ = "lattice_headers"
    
    id = Column(String if not is_postgres else UUID(as_uuid=True), 
                primary_key=True, 
                default=lambda: uuid_lib.uuid4() if is_postgres else str(uuid_lib.uuid4()))
    session_id = Column(String if not is_postgres else UUID(as_uuid=True), 
                       ForeignKey("upload_sessions.id", ondelete="CASCADE"), 
                       index=True)
    category = Column(String, nullable=True, index=True)  # NULL for general headers, category name for specific
    headers = Column(JSONB if is_postgres else Text, nullable=False)  # List of header strings
    apply_to_all = Column(Boolean, default=False, index=True)  # True for general headers
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    session = relationship("UploadSession", back_populates="lattice_headers")


class LatticeResult(Base):
    """Store individual lattice cell results"""
    __tablename__ = "lattice_results"
    
    id = Column(String if not is_postgres else UUID(as_uuid=True), 
                primary_key=True, 
                default=lambda: uuid_lib.uuid4() if is_postgres else str(uuid_lib.uuid4()))
    session_id = Column(String if not is_postgres else UUID(as_uuid=True), 
                       ForeignKey("upload_sessions.id", ondelete="CASCADE"), 
                       index=True)
    file_id = Column(String if not is_postgres else UUID(as_uuid=True), 
                    ForeignKey("uploaded_files.id", ondelete="CASCADE"), 
                    index=True)
    header_key = Column(String, nullable=False, index=True)  # Original header text
    header_category = Column(String, nullable=True, index=True)  # Which category this header belongs to (if any)
    result_value = Column(Text, nullable=False)  # The actual result (can be text, number as string, "Not Found", etc.)
    result_type = Column(String, nullable=True)  # "text", "numeric", "not_found", "not_applicable"
    agent_type = Column(String, nullable=True)  # "finance", "legal", "general"
    confidence_score = Column(Numeric(3, 2), nullable=True)  # Agent confidence if available
    processing_time_ms = Column(Integer, nullable=True)  # How long this cell took to process
    processed_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    error_message = Column(Text, nullable=True)  # If processing failed
    
    # Relationships
    session = relationship("UploadSession", back_populates="lattice_results")
    file = relationship("UploadedFile", back_populates="lattice_results")
    
    # Add table-level indexes for fast lattice queries
    __table_args__ = (
        Index('idx_lattice_lookup', 'session_id', 'file_id', 'header_key'),
        Index('idx_lattice_session_header', 'session_id', 'header_key'),
        Index('idx_lattice_file_header', 'file_id', 'header_key'),
        Index('idx_lattice_category_header', 'header_category', 'header_key'),
        Index('idx_lattice_result_type', 'result_type', 'processed_at'),
    )


# Create indexes explicitly for better performance
if is_postgres:
    from sqlalchemy import Index
    
    # Composite indexes for common queries
    Index('idx_session_status_created', UploadSession.status, UploadSession.created_at)
    Index('idx_file_session_status', UploadedFile.session_id, UploadedFile.status)
    Index('idx_processing_file_type', FileProcessingResult.file_id, FileProcessingResult.processing_type)
    Index('idx_file_dd_category', UploadedFile.due_diligence_category, UploadedFile.categorized_at)
    Index('idx_file_session_category', UploadedFile.session_id, UploadedFile.due_diligence_category)
    Index('idx_file_categorization_status', UploadedFile.categorization_status, UploadedFile.session_id)
    Index('idx_session_categorization_progress', UploadSession.categorization_completed, UploadSession.categorization_total)
    
    # Lattice-specific indexes
    Index('idx_lattice_header_session_category', LatticeHeader.session_id, LatticeHeader.category)
    Index('idx_lattice_header_apply_all', LatticeHeader.apply_to_all, LatticeHeader.session_id)
    Index('idx_session_lattice_status', UploadSession.lattice_status, UploadSession.lattice_started_at)


# Create tables
Base.metadata.create_all(bind=engine)


# Dependency to get DB session
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Async session support for future use
try:
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
    
    if is_postgres:
        # Create async engine for PostgreSQL
        async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")
        async_engine = create_async_engine(
            async_database_url,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            pool_timeout=settings.database_pool_timeout,
            pool_pre_ping=True,
            echo=False
        )
        
        AsyncSessionLocal = async_sessionmaker(
            async_engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )
        
        async def get_async_db() -> AsyncSession:
            async with AsyncSessionLocal() as session:
                yield session
    else:
        # Async not supported for SQLite in production
        async_engine = None
        AsyncSessionLocal = None
        async def get_async_db():
            raise NotImplementedError("Async database not supported with SQLite")
            
except ImportError:
    # asyncpg not installed
    async_engine = None
    AsyncSessionLocal = None
    async def get_async_db():
        raise NotImplementedError("asyncpg not installed")
import os
import re
import time
import asyncio
from typing import List, Dict, Any
from pydantic import BaseModel, Field, create_model
from pydantic_ai import Agent
from dotenv import load_dotenv

from api.utils.logger import logger

# Load environment variables
load_dotenv()

def create_finance_lattice_model(headers: List[str]) -> type[BaseModel]:
    """
    Dynamically create a Pydantic model based on financial lattice headers
    
    Args:
        headers: List of financial analysis headers
        
    Returns:
        Dynamically created Pydantic model class
    """
    logger.info(f"Creating finance lattice model with {len(headers)} headers")
    logger.debug(f"Finance headers: {headers}")
    
    fields = {}
    
    for header in headers:
        # Convert header to valid Python field name
        field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
        field_name = re.sub(r'_+', '_', field_name).strip('_')
        
        # Ensure field name doesn't start with number
        if field_name and field_name[0].isdigit():
            field_name = f"metric_{field_name}"
        
        # All fields are treated equally - let the AI agent decide the appropriate format
        fields[field_name] = (
            str,
            Field(
                ...,
                description=f"Analysis result for: {header}. Be ultra-concise (max 50 chars). Include units for numbers (%, $, x). Use 'Not Found' if no relevant information.",
                max_length=50,
                example="22.5% or $2.3M or Strong growth or Not Found"
            )
        )
    
    model_class = create_model('FinanceLatticeOutput', **fields)
    logger.info(f"Created dynamic Pydantic model '{model_class.__name__}' with {len(fields)} fields")
    
    return model_class


def create_finance_lattice_agent(headers: List[str]) -> Agent:
    """
    Create a Pydantic AI agent for financial lattice analysis
    
    Args:
        headers: List of financial headers to analyze
        
    Returns:
        Configured Pydantic AI agent
    """
    start_time = time.time()
    logger.info(f"💰 Creating finance lattice agent with {len(headers)} headers")
    logger.debug(f"Finance agent headers: {headers}")
    
    # Create dynamic result model
    ResultModel = create_finance_lattice_model(headers)
    
    # Create detailed system prompt for financial analysis
    system_prompt = f"""You are an expert financial analyst with extensive experience in financial statement analysis, valuation, and financial modeling.

LATTICE HEADERS TO ANALYZE:
{chr(10).join(f'- {h}' for h in headers)}

CRITICAL INSTRUCTIONS:
1. For each header, provide ULTRA-CONCISE answers (max 50 characters)
2. Analyze the document content and determine the most appropriate response format
3. For numerical data: include appropriate units (%, $, x, etc.)
4. For qualitative assessments: be brief and specific
5. Use "Not Found" if information is not explicitly stated in the document
6. DO NOT make assumptions - only report what's clearly stated in the financial document

RESPONSE FORMAT EXAMPLES:
- Financial metrics: "22.5%", "$2.3M", "1.34x", "15% YoY"
- Qualitative analysis: "Strong performance", "Declining trend", "Stable margins"
- Missing data: "Not Found"

DOCUMENT ANALYSIS APPROACH:
1. Read the financial document content carefully
2. Look for relevant data for each header
3. Extract exact information as stated in the document
4. Determine the most concise way to present the information
5. Be precise and factual - avoid interpretation
"""
    
    # Get API configuration
    api_key = os.getenv("OPENAI_API_KEY")
    model_name = os.getenv("OPENAI_MODEL_NAME", "gpt-4")
    
    if not api_key:
        api_key = os.getenv("AGENTIC_API_KEY")
        model_name = "agentic-large"
        base_url = os.getenv("AGENTIC_BASE_URL")
        logger.info("Using Agentic API for finance agent")
    else:
        base_url = None
        logger.info("Using OpenAI API for finance agent")
    
    # Create agent with appropriate configuration
    model_settings = {
        'temperature': 0.1,  # Low temperature for consistent financial analysis
        'max_tokens': 1000,  # Enough for all fields
    }
    
    if api_key:
        model_settings['api_key'] = api_key
    if base_url:
        model_settings['base_url'] = base_url
    
    agent = Agent(
        model=f'openai:{model_name}' if not base_url else f'openai:{model_name}',
        result_type=ResultModel,
        system_prompt=system_prompt,
        model_settings=model_settings
    )
    
    creation_time = time.time() - start_time
    logger.info(f"✅ Finance lattice agent created successfully in {creation_time:.2f}s")
    logger.debug(f"Agent model: {model_name}, Temperature: 0.1")
    
    return agent


async def process_finance_document(
    agent: Agent, 
    content: str, 
    filename: str,
    headers: List[str]
) -> Dict[str, Any]:
    """
    Process a financial document with detailed logging
    
    Args:
        agent: The Pydantic AI agent
        content: Document content
        filename: Name of the file being processed
        headers: List of headers being analyzed
        
    Returns:
        Dictionary with processing results and metadata
    """
    start_time = time.time()
    logger.info(f"📊 Starting financial analysis for document: {filename}")
    logger.debug(f"Document content length: {len(content)} characters")
    logger.debug(f"Headers to analyze: {headers}")
    
    try:
        # Prepare input for agent
        prompt = f"Filename: {filename}\n\nFinancial Document Content:\n{content[:4000]}"
        logger.debug(f"Prepared prompt for {filename} (truncated to 4000 chars)")
        
        # Run agent analysis
        logger.debug(f"Starting agent analysis for {filename}")
        agent_start = time.time()
        
        result = await agent.run(prompt)
        
        agent_time = time.time() - agent_start
        logger.info(f"✅ Finance agent analysis completed for {filename} in {agent_time:.2f}s")
        
        # Analyze results
        field_results = {}
        populated_fields = 0
        not_found_fields = 0
        
        for header in headers:
            field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
            field_name = re.sub(r'_+', '_', field_name).strip('_')
            
            if field_name and field_name[0].isdigit():
                field_name = f"metric_{field_name}"
            
            value = getattr(result.data, field_name, "Not Found")
            field_results[header] = value
            
            if value and str(value).lower() not in ['not found', 'not applicable', 'n/a', '']:
                populated_fields += 1
                logger.debug(f"Finance result: {header} = {value}")
            else:
                not_found_fields += 1
                logger.debug(f"Finance result: {header} = Not Found")
        
        total_time = time.time() - start_time
        success_rate = (populated_fields / len(headers)) * 100 if headers else 0
        
        logger.info(f"✅ Financial document {filename} completed in {total_time:.2f}s")
        logger.info(f"📊 Finance results: {populated_fields}/{len(headers)} fields populated ({success_rate:.1f}% success rate)")
        
        return {
            "status": "success",
            "filename": filename,
            "processing_time": total_time,
            "agent_time": agent_time,
            "fields_populated": populated_fields,
            "fields_not_found": not_found_fields,
            "success_rate": success_rate,
            "results": field_results,
            "raw_result": result
        }
        
    except asyncio.TimeoutError:
        total_time = time.time() - start_time
        logger.warning(f"⏰ Financial analysis timeout for {filename} after {total_time:.2f}s")
        return {
            "status": "timeout",
            "filename": filename,
            "processing_time": total_time,
            "error": "Processing timeout"
        }
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"❌ Financial analysis error for {filename} after {total_time:.2f}s: {str(e)}")
        logger.error(f"Financial analysis error details for {filename}", exc_info=True)
        return {
            "status": "error",
            "filename": filename,
            "processing_time": total_time,
            "error": str(e)
        }


def normalize_finance_field_name(header: str) -> str:
    """
    Normalize header to valid Python field name for financial fields
    
    Args:
        header: Original header string
        
    Returns:
        Normalized field name
    """
    field_name = re.sub(r'[^a-zA-Z0-9_]', '_', header.lower())
    field_name = re.sub(r'_+', '_', field_name).strip('_')
    
    if field_name and field_name[0].isdigit():
        field_name = f"metric_{field_name}"
    
    return field_name


# Test function for development
async def test_finance_agent():
    """Test function for finance agent development"""
    test_headers = [
        "Revenue Quality and Growth Sustainability",
        "EBITDA Margin Trends and Quality", 
        "Working Capital Efficiency",
        "Cash Flow Generation Quality",
        "Debt-to-Equity Ratio"
    ]
    
    agent = create_finance_lattice_agent(test_headers)
    
    test_content = """
    FINANCIAL STATEMENTS SUMMARY
    
    Revenue for 2023: $15.2M, representing 25% year-over-year growth
    EBITDA Margin: 22.5% in Q4 2023, improved from 18% in Q3
    Working Capital: $2.3M positive, efficiency ratio of 1.8x
    Operating Cash Flow: $850K positive for the quarter
    Total Debt: $5.1M, Total Equity: $3.8M, Debt-to-Equity ratio of 1.34x
    """
    
    result = await process_finance_document(agent, test_content, "test_financials.pdf", test_headers)
    print(f"Test result: {result}")


if __name__ == "__main__":
    asyncio.run(test_finance_agent())
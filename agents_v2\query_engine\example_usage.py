"""
Example usage of the Query Engine component.
This demonstrates how to use the Query Engine to process documents and answer queries.
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

from base import QueryEngine, print_query_results

async def example_usage():
    """
    Example demonstrating Query Engine usage with sample documents.
    """
    print("🚀 QUERY ENGINE EXAMPLE")
    print("=" * 50)
    
    # Initialize Query Engine
    query_engine = QueryEngine()
    
    # Example document paths (update these to your actual PDF paths)
    pdf_paths = [
        "../../mp_materials/pdfs/form-10-k.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf",
        "../../resido/pdfs/form-10-k.pdf",
    ]
    
    # Step 1: Initialize with documents
    print("📂 Initializing with documents...")
    init_results = await query_engine.initialize_with_documents(pdf_paths)
    
    if not init_results["success"]:
        print(f"❌ Initialization failed: {init_results.get('error')}")
        return
    
    print("✅ Initialization successful!")
    
    # Step 2: Example queries
    example_queries = [
        {
            "query": "What is the total revenue for each company?",
            "type": "lattice",  # Force lattice extraction
            "description": "Lattice extraction example - structured data extraction"
        },
        {
            "query": "What are the main business risks mentioned in these documents?",
            "type": "rag",  # Force RAG search
            "description": "RAG search example - semantic search and synthesis"
        },
        {
            "query": "Extract the key financial metrics",
            "type": "auto",  # Auto-detect (should choose lattice)
            "description": "Auto-detection example - let engine choose best approach"
        }
    ]
    
    # Process each example query
    for i, example in enumerate(example_queries, 1):
        print(f"\n{'='*60}")
        print(f"EXAMPLE {i}: {example['description']}")
        print(f"Query: {example['query']}")
        print(f"Mode: {example['type']}")
        print('='*60)
        
        # Process the query
        results = await query_engine.process_query(
            example["query"], 
            example["type"]
        )
        
        # Display results
        print_query_results(results)
        
        # Wait a moment between queries
        await asyncio.sleep(1)
    
    print(f"\n🎉 Example completed! The Query Engine successfully processed {len(example_queries)} queries.")

async def simple_test():
    """
    Simple test with minimal setup for quick verification.
    """
    print("🧪 SIMPLE QUERY ENGINE TEST")
    print("=" * 40)
    
    # Test with a single document (update path as needed)
    test_pdf = "../../mp_materials/pdfs/form-10-k.pdf"
    
    if not Path(test_pdf).exists():
        print(f"❌ Test PDF not found: {test_pdf}")
        print("Please update the path in simple_test() function")
        return
    
    query_engine = QueryEngine()
    
    # Initialize with single document
    print(f"📄 Testing with: {Path(test_pdf).name}")
    init_results = await query_engine.initialize_with_documents([test_pdf])
    
    if init_results["success"]:
        print("✅ Initialization successful!")
        
        # Test query
        test_query = "What is the company name and revenue?"
        print(f"🔍 Test query: {test_query}")
        
        results = await query_engine.process_query(test_query)
        print_query_results(results)
        
    else:
        print(f"❌ Initialization failed: {init_results.get('error')}")

if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Full example with multiple documents")
    print("2. Simple test with single document")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(example_usage())
    elif choice == "2":
        asyncio.run(simple_test())
    else:
        print("Invalid choice. Running simple test...")
        asyncio.run(simple_test())

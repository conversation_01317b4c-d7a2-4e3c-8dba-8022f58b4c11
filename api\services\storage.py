import boto3
from botocore.client import Config
from botocore.exceptions import Client<PERSON>rror
from typing import Optional, Dict, Any
import os
from datetime import datetime

from api.config import settings
from api.utils.logger import logger


class DigitalOceanSpacesService:
    def __init__(self):
        self.client = boto3.client(
            's3',
            endpoint_url=settings.do_spaces_endpoint.rstrip('/'),
            aws_access_key_id=settings.do_spaces_access_key,
            aws_secret_access_key=settings.do_spaces_secret_key,
            config=Config(signature_version='s3v4', region_name=settings.do_spaces_region)
        )
        self.bucket_name = settings.do_spaces_bucket
        logger.info(f"Initialized DigitalOcean Spaces client for bucket: {self.bucket_name}")
    
    def generate_presigned_url(
        self, 
        object_key: str, 
        expiration: int = None,
        http_method: str = "PUT",
        content_type: Optional[str] = None
    ) -> Optional[str]:
        """Generate a presigned URL for uploading files"""
        try:
            if expiration is None:
                expiration = settings.presigned_url_expiry
            
            params = {
                'Bucket': self.bucket_name,
                'Key': object_key
            }
            
            # Add content type if provided
            if content_type and http_method == "PUT":
                params['ContentType'] = content_type
            
            url = self.client.generate_presigned_url(
                ClientMethod='put_object' if http_method == "PUT" else 'get_object',
                Params=params,
                ExpiresIn=expiration
            )
            
            logger.info(f"Generated presigned URL for {object_key}")
            return url
            
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {str(e)}", exc_info=True)
            return None
    
    def check_object_exists(self, object_key: str) -> bool:
        """Check if an object exists in the bucket"""
        try:
            self.client.head_object(Bucket=self.bucket_name, Key=object_key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error(f"Error checking object existence: {str(e)}")
            return False
    
    def get_object_metadata(self, object_key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for an object"""
        try:
            response = self.client.head_object(Bucket=self.bucket_name, Key=object_key)
            return {
                'size': response.get('ContentLength', 0),
                'content_type': response.get('ContentType', 'application/octet-stream'),
                'last_modified': response.get('LastModified'),
                'etag': response.get('ETag', '').strip('"')
            }
        except ClientError as e:
            logger.error(f"Error getting object metadata: {str(e)}")
            return None
    
    def list_objects_by_prefix(self, prefix: str) -> list:
        """List all objects with a given prefix"""
        try:
            response = self.client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )
            
            objects = []
            for obj in response.get('Contents', []):
                objects.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified']
                })
            
            return objects
            
        except ClientError as e:
            logger.error(f"Error listing objects: {str(e)}")
            return []
    
    def delete_object(self, object_key: str) -> bool:
        """Delete an object from the bucket"""
        try:
            self.client.delete_object(Bucket=self.bucket_name, Key=object_key)
            logger.info(f"Deleted object: {object_key}")
            return True
        except ClientError as e:
            logger.error(f"Error deleting object: {str(e)}")
            return False
    
    def delete_objects_by_prefix(self, prefix: str) -> int:
        """Delete all objects with a given prefix"""
        objects = self.list_objects_by_prefix(prefix)
        deleted_count = 0
        
        for obj in objects:
            if self.delete_object(obj['key']):
                deleted_count += 1
        
        return deleted_count
    
    def generate_unique_key(self, session_id: str, filename: str) -> str:
        """Generate a unique object key for a file"""
        # Clean filename
        safe_filename = "".join(c for c in filename if c.isalnum() or c in ".-_")
        
        # Create hierarchical structure: uploads/{session_id}/{timestamp}_{filename}
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        return f"uploads/{session_id}/{timestamp}_{safe_filename}"


# Singleton instance
storage_service = DigitalOceanSpacesService()
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from datetime import datetime, timezone
import json
import asyncio

from api.models.database import get_db, UploadSession, UploadedFile, DueDiligenceCategory, CategorizationStatus, to_db_id, from_db_id
from api.services.parallel_categorization import parallel_categorization_service
from api.utils.logger import logger


# Create router
router = APIRouter(prefix="/process", tags=["process"])


@router.post("/{session_id}")
async def start_categorization(
    session_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Start the categorization process for all files in a session.
    Returns immediately with job status while processing continues in background.
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Check if categorization is already in progress
        if session.categorization_started_at and not session.categorization_completed_at:
            # Check if it's been stuck for more than 10 minutes
            time_since_start = (datetime.now(timezone.utc) - session.categorization_started_at).total_seconds()
            if time_since_start > 600:  # 10 minutes
                logger.warning(f"Detected stuck categorization for session {session_id}, resetting...")
                # Reset the session
                session.categorization_started_at = None
                session.categorization_total = 0
                session.categorization_completed = 0
                session.categorization_failed = 0
                
                # Reset stuck files
                db.query(UploadedFile).filter(
                    UploadedFile.session_id == to_db_id(session_id),
                    UploadedFile.categorization_status == CategorizationStatus.IN_PROGRESS
                ).update({
                    "categorization_status": CategorizationStatus.PENDING,
                    "categorization_started_at": None
                }, synchronize_session=False)
                db.commit()
            else:
                return {
                    "session_id": from_db_id(session.id),
                    "status": "in_progress",
                    "message": "Categorization is already in progress",
                    "started_at": session.categorization_started_at.isoformat(),
                    "progress": {
                        "total": session.categorization_total,
                        "completed": session.categorization_completed,
                        "failed": session.categorization_failed
                    },
                    "duration_seconds": round(time_since_start, 2)
                }
        
        # Count files that need categorization
        pending_files = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.categorization_status == CategorizationStatus.PENDING,
            UploadedFile.bucket_key.isnot(None)
        ).count()
        
        if pending_files == 0:
            # Check if all files are already categorized
            total_files = db.query(UploadedFile).filter(
                UploadedFile.session_id == to_db_id(session_id),
                UploadedFile.bucket_key.isnot(None)
            ).count()
            
            if total_files == 0:
                return {
                    "session_id": from_db_id(session.id),
                    "status": "no_files",
                    "message": "No files found for processing"
                }
            else:
                return {
                    "session_id": from_db_id(session.id),
                    "status": "completed",
                    "message": "All files are already categorized",
                    "progress": {
                        "total": total_files,
                        "completed": total_files,
                        "failed": 0
                    }
                }
        
        # Start background categorization
        background_tasks.add_task(
            parallel_categorization_service.categorize_session,
            session_id
        )
        
        return {
            "session_id": from_db_id(session.id),
            "status": "started",
            "message": f"Started categorization for {pending_files} files",
            "files_to_process": pending_files,
            "poll_endpoint": f"/process/{session_id}/status"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting categorization: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/status")
async def get_categorization_status(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get the current status of categorization for a session.
    Use this endpoint to poll for progress.
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get detailed file status
        files_query = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.bucket_key.isnot(None)
        )
        
        # Count by status
        status_counts = {
            "pending": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.PENDING).count(),
            "in_progress": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.IN_PROGRESS).count(),
            "completed": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.COMPLETED).count(),
            "failed": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.FAILED).count()
        }
        
        total_files = sum(status_counts.values())
        
        # Count by category
        category_counts = {}
        for category in DueDiligenceCategory:
            count = files_query.filter(UploadedFile.due_diligence_category == category).count()
            if count > 0:
                category_counts[category.value] = count
        
        # Determine overall status
        if session.categorization_completed_at:
            overall_status = "completed"
        elif session.categorization_started_at:
            overall_status = "in_progress"
        else:
            overall_status = "not_started"
        
        # Calculate progress percentage
        progress_percentage = 0
        if total_files > 0:
            progress_percentage = ((status_counts["completed"] + status_counts["failed"]) / total_files) * 100
        
        # Get files with their current status
        files_detail = []
        for file in files_query.all():
            file_info = {
                "filename": file.filename,
                "categorization_status": file.categorization_status.value if file.categorization_status else "pending",
                "category": file.due_diligence_category.value if file.due_diligence_category else "unknown",
                "confidence": float(file.categorization_confidence) if file.categorization_confidence else 0.0
            }
            
            # Include insights for completed files
            if file.categorization_status == CategorizationStatus.COMPLETED and file.agent_insights:
                file_info["insights"] = file.agent_insights
            
            files_detail.append(file_info)
        
        response = {
            "session_id": from_db_id(session.id),
            "status": overall_status,
            "progress_percentage": round(progress_percentage, 2),
            "started_at": session.categorization_started_at.isoformat() if session.categorization_started_at else None,
            "completed_at": session.categorization_completed_at.isoformat() if session.categorization_completed_at else None,
            "file_status_counts": status_counts,
            "category_distribution": category_counts,
            "total_files": total_files,
            "files": files_detail
        }
        
        # Add duration if in progress or completed
        if session.categorization_started_at:
            end_time = session.categorization_completed_at or datetime.now(timezone.utc)
            duration = (end_time - session.categorization_started_at).total_seconds()
            response["duration_seconds"] = round(duration, 2)
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting categorization status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}")
async def get_session_summary(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get a summary of files in the session with their categorization results.
    """
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get all files with their details
        files_info = []
        category_counts = {}
        
        for file in session.files:
            if file.bucket_key:  # Only include files that were uploaded
                file_info = {
                    "filename": file.filename,
                    "file_size": file.file_size,
                    "content_type": file.content_type,
                    "status": file.status.value if file.status else "unknown",
                    "bucket_key": file.bucket_key,
                    "due_diligence_category": file.due_diligence_category.value if file.due_diligence_category else "unknown",
                    "categorization_confidence": float(file.categorization_confidence) if file.categorization_confidence else 0.0,
                    "categorization_status": file.categorization_status.value if file.categorization_status else "pending",
                    "categorized_at": file.categorized_at.isoformat() if file.categorized_at else None
                }
                
                # Include insights if available
                if file.agent_insights:
                    file_info["insights"] = file.agent_insights
                
                files_info.append(file_info)
                
                # Count categories
                category = file.due_diligence_category.value if file.due_diligence_category else "unknown"
                category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            "session_id": from_db_id(session.id),
            "company_name": session.company_name,
            "session_type": session.session_type,
            "total_files": session.total_files,
            "uploaded_files": len(files_info),
            "files": files_info,
            "session_status": session.status.value if session.status else "unknown",
            "category_summary": category_counts,
            "categorization_complete": session.categorization_completed_at is not None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session summary: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/category/{category}")
async def get_files_by_category(
    session_id: str,
    category: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get all files in a session that belong to a specific due diligence category.
    """
    try:
        # Validate category
        try:
            dd_category = DueDiligenceCategory(category)
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid category. Must be one of: {', '.join([c.value for c in DueDiligenceCategory])}"
            )
        
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get files by category
        files = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.due_diligence_category == dd_category,
            UploadedFile.bucket_key.isnot(None)  # Only uploaded files
        ).all()
        
        files_info = []
        for file in files:
            file_data = {
                "filename": file.filename,
                "file_size": file.file_size,
                "content_type": file.content_type,
                "bucket_key": file.bucket_key,
                "categorization_confidence": float(file.categorization_confidence) if file.categorization_confidence else 0.0,
                "categorized_at": file.categorized_at.isoformat() if file.categorized_at else None
            }
            
            # Include insights if available
            if file.agent_insights:
                file_data["insights"] = file.agent_insights
            
            files_info.append(file_data)
        
        return {
            "session_id": from_db_id(session.id),
            "category": category,
            "total_files": len(files_info),
            "files": files_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting files by category: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
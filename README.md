# Quantera

## Setup

1. Update submodules:
```bash
git submodule update --init --recursive
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Copy the example environment file and add your API keys:
```bash
cp env.example .env
```

4. Edit `.env` with your actual API keys.

## Running

### Neo4j Database (Terminal 1)
```bash
cd memory-graph
docker compose up
```

### Qdrant Vector Database (Terminal 2)
```bash
docker run -p 6333:6333 -p 6334:6334 \
    -v "$(pwd)/qdrant_storage:/qdrant/storage:z" \
    qdrant/qdrant
```

### Memory Graph (Terminal 3)
```bash
cd memory-graph
python main.py
```

### Streamlit App (Terminal 4)
```bash
streamlit run streamlit_app.py
```

The memory graph API will run on `http://localhost:8000` and Streamlit on `http://localhost:8501`. 
# Quantera Upload API Documentation

## Overview

The Quantera Upload API provides endpoints for bulk document uploads with job polling capabilities. It supports both production uploads via presigned URLs and development mode for processing local files.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, no authentication is required (development mode).

---

## Endpoints

### 1. Health Check

**GET** `/api/v1/health`

Check if the API is running and healthy.

**Response:**
```json
{
  "status": "healthy",
  "service": "upload-api",
  "version": "1.0.0"
}
```

---

## Upload Endpoints

### 2. Prepare Upload

**POST** `/api/v1/upload/prepare`

Create a new upload session and get presigned URLs for direct file uploads to S3.

**Request Body:**
```json
{
  "filenames": ["file1.pdf", "file2.xlsx", "file3.pdf"]
}
```

**Optional Headers:**
- `user-id`: User identifier
- `company-name`: Company name for the session
- `session-type`: Type of session (e.g., "quarterly_report")

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "presigned_urls": [
    {
      "filename": "file1.pdf",
      "upload_url": "https://storage.digitaloceanspaces.com/...",
      "file_id": "660e8400-e29b-41d4-a716-446655440001",
      "bucket_key": "uploads/550e8400/file1.pdf"
    }
  ],
  "expires_in_seconds": 900
}
```

### 3. Complete Upload

**POST** `/api/v1/upload/complete`

Mark files as uploaded after successfully uploading to S3.

**Request Body:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "files": [
    {
      "filename": "file1.pdf",
      "bucket_id": "uploads/550e8400/file1.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf"
    }
  ]
}
```

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "total_files": 3,
  "processed_files": 0,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "files": [...]
}
```

### 4. Start Processing

**POST** `/api/v1/upload/process/{session_id}`

Start processing files in a session (triggers background processing).

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "message": "Processing started for 3 files"
}
```

### 5. Get Session Status

**GET** `/api/v1/upload/status/{session_id}`

Poll this endpoint to check upload and processing status.

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "total_files": 3,
  "processed_files": 3,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "files": [
    {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "filename": "file1.pdf",
      "status": "processed",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "created_at": "2024-01-01T00:00:00"
    }
  ]
}
```

### 6. Get Processing Results

**GET** `/api/v1/upload/results/{session_id}`

Get detailed processing results for a session.

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "user_id": "user123",
  "company_name": "Acme Corp",
  "session_type": "quarterly_report",
  "total_files": 3,
  "processed_files": 3,
  "total_size_bytes": 3072000,
  "file_stats": {
    "pending": 0,
    "uploaded": 0,
    "processing": 0,
    "processed": 3,
    "failed": 0
  },
  "processing_results": [
    {
      "file_id": "660e8400-e29b-41d4-a716-446655440001",
      "filename": "file1.pdf",
      "processing_type": "initial_processing",
      "has_extracted_text": true,
      "has_ai_summary": true,
      "embeddings_generated": true,
      "entity_counts": {
        "companies": 2,
        "people": 3,
        "dates": 4,
        "amounts": 2
      },
      "document_type": "financial_report",
      "classification_confidence": 0.95
    }
  ]
}
```

---

## Process Endpoints

### 7. Get Session Files with Due Diligence Categorization

**GET** `/api/v1/process/{session_id}`

Get list of filenames for all files in a session with automatic due diligence categorization.

**Features:**
- Automatically categorizes uncategorized files in the background
- Returns due diligence category for each file
- Provides category distribution summary
- Includes categorization confidence scores

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "company_name": "Acme Corp",
  "session_type": "quarterly_report",
  "total_files": 3,
  "uploaded_files": 3,
  "filenames": ["file1.pdf", "file2.xlsx", "file3.pdf"],
  "files_detail": [
    {
      "filename": "file1.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "status": "uploaded",
      "bucket_key": "uploads/session-id/file1.pdf",
      "due_diligence_category": "financial_due_diligence",
      "categorization_confidence": 0.92,
      "categorized_at": "2024-01-01T00:00:00",
      "categorization_reasoning": "Contains financial statements and revenue data",
      "categorization_indicators": ["revenue", "financial statements", "cash flow"]
    }
  ],
  "session_status": "pending",
  "category_summary": {
    "financial_due_diligence": 2,
    "legal_due_diligence": 1,
    "unknown": 0
  },
  "uncategorized_files": 0,
  "categorization_in_progress": false
}
```

### 8. Get Files by Category

**GET** `/api/v1/process/{session_id}/category/{category}`

Get all files in a session that belong to a specific due diligence category.

**Path Parameters:**
- `session_id`: Session identifier
- `category`: Due diligence category (financial_due_diligence, technical_due_diligence, legal_due_diligence, operational_due_diligence, market_due_diligence, unknown)

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "category": "financial_due_diligence",
  "total_files": 2,
  "files": [
    {
      "filename": "quarterly_report.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf",
      "bucket_key": "uploads/session-id/quarterly_report.pdf",
      "categorization_confidence": 0.95,
      "categorized_at": "2024-01-01T00:00:00",
      "categorization_reasoning": "Contains financial statements and revenue data",
      "categorization_indicators": ["revenue", "financial statements", "cash flow"]
    }
  ]
}
```

### 9. Get Category Distribution

**GET** `/api/v1/process/{session_id}/categories`

Get the distribution of files across due diligence categories for a session.

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "total_files": 10,
  "categorized_files": 8,
  "uncategorized_files": 2,
  "categorization_progress": 80.0,
  "category_distribution": {
    "financial_due_diligence": {
      "count": 4,
      "avg_confidence": 0.89
    },
    "legal_due_diligence": {
      "count": 2,
      "avg_confidence": 0.76
    },
    "technical_due_diligence": {
      "count": 2,
      "avg_confidence": 0.82
    },
    "unknown": {
      "count": 2,
      "avg_confidence": 0.0
    }
  }
}
```

---

## Development Endpoints (Dev Mode Only)

### 8. Process Local Files

**POST** `/api/v1/dev/process`

Process files from local directories (mp_materials/resido) with caching support.

**Request Body:**
```json
{
  "source": "mp_materials",  // "mp_materials", "resido", or "both"
  "file_types": ["pdf", "xlsx"],  // Optional: filter by file types
  "subdirectory": "quarterly_reports",  // Optional: specific subdirectory
  "auto_process": true,  // Start processing after upload
  "user_id": "dev_user"  // Optional: user identifier
}
```

**Response (First Call):**
```json
{
  "status": "success",
  "session_id": "deterministic-session-id",
  "total_files": 103,
  "message": "Upload started in background. Poll /upload/status/{session_id} for progress",
  "cached": false,
  "auto_process": true
}
```

**Response (Subsequent Calls - Cached):**
```json
{
  "status": "success",
  "session_id": "deterministic-session-id",
  "message": "Using existing uploaded files from cache",
  "cached": true,
  "auto_process": true
}
```

### 9. List Local Files

**GET** `/api/v1/dev/list-local-files?source=mp_materials&file_types=pdf,xlsx&subdirectory=reports`

List all files that would be processed without uploading.

**Query Parameters:**
- `source`: "mp_materials", "resido", or "both"
- `file_types`: Comma-separated file types (optional)
- `subdirectory`: Specific subdirectory (optional)

**Response:**
```json
[
  {
    "full_path": "./mp_materials/report.pdf",
    "relative_path": "report.pdf",
    "filename": "report.pdf",
    "size": 1024000,
    "extension": ".pdf",
    "directory": ".",
    "source": "mp_materials"
  }
]
```

### 10. Get Dev Statistics

**GET** `/api/v1/dev/stats`

Get statistics about available local files.

**Response:**
```json
{
  "mp_materials": {
    "total_files": 103,
    "pdf_files": 100,
    "excel_files": 3,
    "total_size_mb": 256.5,
    "directories": [".", "quarterly", "annual"]
  },
  "resido": {
    "total_files": 169,
    "pdf_files": 150,
    "excel_files": 19,
    "total_size_mb": 512.3,
    "directories": [".", "reports", "data"]
  },
  "combined": {
    "total_files": 272,
    "total_size_mb": 768.8
  }
}
```

### 11. Get Cache Status

**GET** `/api/v1/dev/cache/status`

View all cached dev sessions.

**Response:**
```json
{
  "cache_file": ".cache/dev_sessions.json",
  "total_sessions": 2,
  "sessions": {
    "mp_materials:all": {
      "session_id": "deterministic-id-1",
      "created_at": "2024-01-01T00:00:00",
      "last_accessed": "2024-01-01T01:00:00",
      "file_count": 103,
      "status": "active"
    },
    "resido:reports": {
      "session_id": "deterministic-id-2",
      "created_at": "2024-01-01T00:00:00",
      "last_accessed": "2024-01-01T01:00:00",
      "file_count": 50,
      "status": "active"
    }
  }
}
```

### 12. Verify Cache

**GET** `/api/v1/dev/cache/verify`

Verify all cached sessions exist in S3 and clean up invalid ones.

**Response:**
```json
{
  "verified": 2,
  "removed": 1,
  "message": "Verified 2 sessions, removed 1 invalid sessions"
}
```

### 13. Clear Cache

**DELETE** `/api/v1/dev/cache`

Clear all cached sessions.

**Response:**
```json
{
  "cleared": 3,
  "message": "Cleared 3 cached sessions"
}
```

---

## Storage Management Endpoints

### 14. List Storage Objects

**GET** `/api/v1/storage/list?prefix=uploads/&max_keys=100`

List objects in storage bucket.

**Query Parameters:**
- `prefix`: Filter by prefix (optional)
- `max_keys`: Maximum number of results (default: 1000)

**Response:**
```json
{
  "objects": [
    {
      "key": "uploads/session-id/file1.pdf",
      "size": 1024000,
      "last_modified": "2024-01-01T00:00:00",
      "storage_class": "STANDARD"
    }
  ],
  "count": 1,
  "truncated": false
}
```

### 15. Delete Storage Object

**DELETE** `/api/v1/storage/delete/{object_key}`

Delete a specific object from storage.

**Response:**
```json
{
  "message": "Object deleted successfully",
  "key": "uploads/session-id/file1.pdf"
}
```

### 16. Cleanup Old Uploads

**POST** `/api/v1/storage/cleanup?days_old=7&dry_run=true`

Clean up uploads older than specified days.

**Query Parameters:**
- `days_old`: Delete files older than this many days (default: 30)
- `dry_run`: If true, only show what would be deleted (default: true)

**Response:**
```json
{
  "mode": "dry_run",
  "found": 10,
  "deleted": 0,
  "total_size_mb": 25.6,
  "objects": ["uploads/old-session/file1.pdf", ...]
}
```

---

## Typical Usage Flow

### Production Upload Flow

1. **Prepare Upload**
   ```bash
   POST /api/v1/upload/prepare
   Body: {"filenames": ["report1.pdf", "data.xlsx"]}
   ```

2. **Upload Files to S3**
   Use the presigned URLs to upload directly to S3

3. **Complete Upload**
   ```bash
   POST /api/v1/upload/complete
   Body: {"session_id": "xxx", "files": [...]}
   ```

4. **Start Processing**
   ```bash
   POST /api/v1/upload/process/{session_id}
   ```

5. **Poll Status**
   ```bash
   GET /api/v1/upload/status/{session_id}
   # Repeat until status is "completed" or "failed"
   ```

6. **Get Results**
   ```bash
   GET /api/v1/upload/results/{session_id}
   ```

### Development Mode Flow

1. **First Time - Process Local Files**
   ```bash
   POST /api/v1/dev/process
   Body: {"source": "mp_materials"}
   # Returns session_id, uploads in background
   ```

2. **Poll Upload Status**
   ```bash
   GET /api/v1/upload/status/{session_id}
   # Wait for uploads to complete
   ```

3. **Subsequent Calls - Use Cache**
   ```bash
   POST /api/v1/dev/process
   Body: {"source": "mp_materials"}
   # Returns immediately with cached session_id
   ```

4. **Get Files for Processing with Categorization**
   ```bash
   GET /api/v1/process/{session_id}
   # Returns list of filenames with due diligence categories
   # Automatically starts categorization for uncategorized files
   ```

5. **Query by Category**
   ```bash
   GET /api/v1/process/{session_id}/category/financial_due_diligence
   # Get only financial due diligence files
   
   GET /api/v1/process/{session_id}/categories
   # Get category distribution summary
   ```

5. **View Cache Status**
   ```bash
   GET /api/v1/dev/cache/status
   ```

---

## Status Values

### Session Status
- `pending`: Session created, files not yet uploaded
- `uploading`: Files are being uploaded
- `processing`: Files are being processed
- `completed`: All files processed successfully
- `failed`: Processing failed

### File Status
- `pending`: File record created
- `uploaded`: File uploaded to S3
- `processing`: File is being processed
- `processed`: File processed successfully
- `failed`: File processing failed

---

## Error Responses

All endpoints return standard error responses:

```json
{
  "detail": "Error message here"
}
```

Common HTTP status codes:
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (session/file not found)
- `403`: Forbidden (dev endpoints in production)
- `500`: Internal Server Error
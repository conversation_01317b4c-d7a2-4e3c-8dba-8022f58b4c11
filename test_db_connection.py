#!/usr/bin/env python3
"""Test database connection and table creation"""

from api.models.database import engine, SessionLocal, UploadSession, UploadedFile, FileProcessingResult
from api.config import settings
from sqlalchemy import inspect, text

def test_connection():
    print(f"Testing connection to: {settings.database_url}")
    
    try:
        # Test basic connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            
            # Check if using PostgreSQL
            if "postgresql" in settings.database_url:
                result = conn.execute(text("SELECT version()"))
                version = result.scalar()
                print(f"✅ PostgreSQL version: {version}")
                
                # List tables
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """))
                tables = [row[0] for row in result]
                print(f"✅ Tables found: {tables}")
            else:
                print("ℹ️  Using SQLite database")
                
        # Test ORM
        db = SessionLocal()
        try:
            # Count sessions
            session_count = db.query(UploadSession).count()
            file_count = db.query(UploadedFile).count()
            result_count = db.query(FileProcessingResult).count()
            
            print(f"\n📊 Database Statistics:")
            print(f"  - Upload Sessions: {session_count}")
            print(f"  - Uploaded Files: {file_count}")
            print(f"  - Processing Results: {result_count}")
            
        finally:
            db.close()
            
        print("\n✅ All database tests passed!")
        
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_connection()
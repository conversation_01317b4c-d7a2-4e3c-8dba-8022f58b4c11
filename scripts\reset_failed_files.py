"""
Reset failed categorization files to pending
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger


def reset_failed_files(session_id: str):
    """Reset failed files back to pending status"""
    
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            # Reset failed files to pending
            reset_sql = """
            UPDATE uploaded_files
            SET 
                categorization_status = 'pending'::categorizationstatus,
                categorization_started_at = NULL,
                agent_insights = NULL
            WHERE 
                session_id::text = :session_id 
                AND categorization_status = 'failed'::categorizationstatus;
            """
            
            result = conn.execute(text(reset_sql), {"session_id": session_id})
            files_reset = result.rowcount
            
            conn.commit()
            
            print(f"✅ Reset {files_reset} failed files to pending status")
            
            # Show current status
            status_sql = """
            SELECT 
                categorization_status,
                COUNT(*) as count
            FROM uploaded_files
            WHERE session_id::text = :session_id
            GROUP BY categorization_status;
            """
            
            result = conn.execute(text(status_sql), {"session_id": session_id})
            print(f"\nCurrent file status:")
            for row in result:
                print(f"   {row[0]}: {row[1]}")
            
            logger.info(f"Reset {files_reset} failed files for session {session_id}")
            
    except Exception as e:
        logger.error(f"Error resetting failed files: {str(e)}", exc_info=True)
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    finally:
        engine.dispose()


if __name__ == "__main__":
    session_id = "188c1893-ebdf-2f5b-b9d4-60208c451927"
    print(f"Resetting failed files for session: {session_id}")
    reset_failed_files(session_id)
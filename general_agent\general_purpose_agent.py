import sys
import asyncio
import os
import openai
from dotenv import load_dotenv
from prompts import GPA_REDUCTION_PROMPT
from llm_client import extract_info_from_page
import datetime
import logging
import importlib.util

if sys.platform.startswith('win'):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# --- Logging Setup ---
log_dir = 'logs'
os.makedirs(log_dir, exist_ok=True)
log_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
log_path = os.path.join(log_dir, f'logs_{log_time}.txt')

# Only log to file, not to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler(log_path, encoding='utf-8')
    ]
)

def print(*args, **kwargs):
    # Suppress all print output to console; only log to file
    msg = ' '.join(str(a) for a in args)
    logging.info(msg)

load_dotenv()
agentic_api_key = os.getenv("AGENTIC_API_KEY")
agentic_base_url = os.getenv("AGENTIC_BASE_URL")

def split_markdown_into_pages(md_text):
    """Splits markdown text by a specific delimiter into pages."""
    pages = [page.strip() for page in md_text.split('================================================================================') if page.strip()]
    return [(i + 1, page) for i, page in enumerate(pages)]

async def reduce_results(page_results, query):
    """This is the 'Reduce' step, combining results into a final summary."""
    valid_results = [(page_num, result) for page_num, result in page_results if result != "Not Found"]
    if not valid_results:
        return "No relevant information found in the document for this query."

    if len(valid_results) == 1:
        return valid_results[0][1]

    client = openai.AsyncOpenAI(api_key=agentic_api_key, base_url=agentic_base_url, max_retries=3, timeout=60.0)
    page_results_text = "\n\n---\n\n".join([f"Page {page_num} Results:\n{result}" for page_num, result in valid_results])
    prompt = GPA_REDUCTION_PROMPT.format(query=query, page_results=page_results_text)
    
    try:
        response = await client.chat.completions.create(
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a research analyst. Synthesize the provided information into a coherent, well-structured report that answers the query."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"❌ Error combining results: {e}")
        return "\n\n---\n\n".join([f"**Page {page_num}:**\n{result}" for page_num, result in valid_results])

async def process_single_document_from_pdf(pdf_path, query):
    """Convert PDF to markdown, then process as before."""
    # Dynamically import the pdf_to_markdown module
    pdf2md_path = os.path.join(os.path.dirname(__file__), '../utils/pdf_to_markdown.py')
    spec = importlib.util.spec_from_file_location("pdf_to_markdown", pdf2md_path)
    pdf_to_markdown = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(pdf_to_markdown)

    # Convert PDF to markdown using LlamaParse (async)
    md_output_dir = 'output'
    os.makedirs(md_output_dir, exist_ok=True)
    base_filename = os.path.splitext(os.path.basename(pdf_path))[0]
    md_path = os.path.join(md_output_dir, f"{base_filename}.md")

    # Use LlamaParse if available, else fallback
    markdown_content = await pdf_to_markdown.convert_pdf_to_markdown_llamaparse(pdf_path)
    if not markdown_content:
        # fallback to pymupdf4llm if LlamaParse fails
        markdown_content = pdf_to_markdown.convert_pdf_to_markdown_pymupdf4llm(pdf_path)
    if not markdown_content:
        # fallback to Jina if both fail
        markdown_content = pdf_to_markdown.convert_pdf_to_markdown_jina(pdf_path)
    if not markdown_content:
        print(f"Failed to convert PDF to markdown: {pdf_path}")
        return

    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)

    # Now process the markdown as before
    with open(md_path, 'r', encoding='utf-8') as f:
        md_text = f.read()

    pages = split_markdown_into_pages(md_text)
    if not pages:
        print("❌ Document is empty or could not be split into pages.")
        return

    # MAP: Process all pages in parallel
    tasks = [extract_info_from_page(page_content, query, page_num) for page_num, page_content in pages]
    page_level_results = await asyncio.gather(*tasks)

    results_with_pagenums = list(zip([p[0] for p in pages], page_level_results))

    # REDUCE: Combine the results
    final_report = await reduce_results(results_with_pagenums, query)

    # Save the final report to a markdown file (just the answer)
    output_filename = os.path.join(md_output_dir, f"agent_response_{log_time}.md")
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(final_report)
    print(f"✅ Report saved to: {output_filename}")
    return output_filename

async def main():
    pdf_path = input("Enter the path to the PDF file: ").strip()
    if not os.path.isfile(pdf_path):
        print(f"File not found: {pdf_path}")
        return
    query = input("Enter your query for this document: ").strip()
    await process_single_document_from_pdf(pdf_path, query)

if __name__ == '__main__':
    asyncio.run(main())
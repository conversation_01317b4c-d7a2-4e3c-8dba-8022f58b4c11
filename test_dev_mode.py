#!/usr/bin/env python3
"""Test development mode functionality"""

import requests
import json
from time import sleep

BASE_URL = "http://localhost:8000/api/v1"

def test_dev_endpoints():
    print("🧪 Testing Development Mode Endpoints\n")
    
    # 1. Test stats endpoint
    print("1️⃣ Testing /dev/stats...")
    response = requests.get(f"{BASE_URL}/dev/stats")
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ Stats retrieved successfully:")
        print(f"   MP Materials: {stats['mp_materials']['total_files']} files, {stats['mp_materials']['total_size_mb']:.2f} MB")
        print(f"   Resido: {stats['resido']['total_files']} files, {stats['resido']['total_size_mb']:.2f} MB")
        print(f"   Combined: {stats['combined']['total_files']} files, {stats['combined']['total_size_mb']:.2f} MB\n")
    else:
        print(f"❌ Failed to get stats: {response.status_code} - {response.text}\n")
    
    # 2. Test list local files
    print("2️⃣ Testing /dev/list-local-files...")
    response = requests.get(f"{BASE_URL}/dev/list-local-files", params={
        "source": "mp_materials",
        "file_types": "pdf",
        "subdirectory": "pdfs"
    })
    if response.status_code == 200:
        files = response.json()
        print(f"✅ Found {len(files)} PDF files in mp_materials/pdfs")
        if files:
            print(f"   First file: {files[0]['filename']} ({files[0]['size'] / 1024:.2f} KB)\n")
    else:
        print(f"❌ Failed to list files: {response.status_code} - {response.text}\n")
    
    # 3. Test storage list
    print("3️⃣ Testing /storage/list...")
    response = requests.get(f"{BASE_URL}/storage/list", params={"limit": 10})
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Storage contains {result['count']} objects (showing first 10)")
        print(f"   Total size: {result['total_size_mb']:.2f} MB\n")
    else:
        print(f"❌ Failed to list storage: {response.status_code} - {response.text}\n")
    
    # 4. Test process local files (small test)
    print("4️⃣ Testing /dev/process with a small subset...")
    response = requests.post(f"{BASE_URL}/dev/process", json={
        "source": "mp_materials",
        "file_types": ["pdf"],
        "subdirectory": "pdfs",
        "auto_process": False,  # Don't trigger processing yet
        "user_id": "test_user"
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Upload completed:")
        print(f"   Session ID: {result['session_id']}")
        print(f"   Uploaded: {result['uploaded']} files")
        print(f"   Failed: {result['failed']} files")
        print(f"   Total size: {result['total_size_mb']:.2f} MB\n")
        
        # Check session status
        session_id = result['session_id']
        sleep(1)  # Give it a moment
        
        print("5️⃣ Checking session status...")
        response = requests.get(f"{BASE_URL}/upload/status/{session_id}")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Session status: {status['status']}")
            print(f"   Files uploaded: {status['total_files']}\n")
        
        # Test storage info for one file
        print("6️⃣ Testing storage info...")
        response = requests.get(f"{BASE_URL}/storage/list", params={
            "prefix": f"uploads/{session_id}/",
            "limit": 1
        })
        if response.status_code == 200 and response.json()['objects']:
            first_object = response.json()['objects'][0]
            object_key = first_object['key']
            
            response = requests.get(f"{BASE_URL}/storage/info/{object_key}")
            if response.status_code == 200:
                info = response.json()
                print(f"✅ Object info retrieved:")
                print(f"   Key: {info['key']}")
                print(f"   Size: {info['size'] / 1024:.2f} KB")
                print(f"   Type: {info['content_type']}\n")
        
        # Test cleanup
        print("7️⃣ Testing session cleanup...")
        response = requests.post(f"{BASE_URL}/storage/cleanup-session/{session_id}")
        if response.status_code == 200:
            cleanup = response.json()
            print(f"✅ Cleanup completed: {cleanup['message']}\n")
    else:
        print(f"❌ Failed to process files: {response.status_code} - {response.text}\n")

def test_production_mode_restriction():
    print("8️⃣ Testing production mode restriction...")
    print("   (Change ENVIRONMENT=production in .env to test this)")
    print("   Dev endpoints should return 403 Forbidden in production mode\n")

if __name__ == "__main__":
    print("=" * 60)
    print("Development Mode Test Script")
    print("=" * 60)
    print("\nMake sure the API is running with: python run_api.py\n")
    
    try:
        # Check if API is running
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is running\n")
            test_dev_endpoints()
            test_production_mode_restriction()
        else:
            print("❌ API health check failed")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
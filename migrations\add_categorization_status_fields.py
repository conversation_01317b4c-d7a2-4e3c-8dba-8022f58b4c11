"""
Migration script to add categorization status tracking fields
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings
from api.utils.logger import logger

def run_migration():
    """Run the migration to add categorization status fields"""
    
    # Create engine
    engine = create_engine(settings.database_url)
    
    # SQL statements to add the new columns
    migration_steps = [
        # Step 1: Create categorization status enum
        """
        CREATE TYPE categorizationstatus AS ENUM (
            'pending',
            'in_progress', 
            'completed',
            'failed'
        );
        """,
        
        # Step 2: Add status tracking columns to uploaded_files
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS categorization_status categorizationstatus DEFAULT 'pending'::categorizationstatus;
        """,
        
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS categorization_started_at TIMESTAMP WITH TIME ZONE;
        """,
        
        """
        ALTER TABLE uploaded_files 
        ADD COLUMN IF NOT EXISTS agent_insights JSONB;
        """,
        
        # Step 3: Add status tracking columns to upload_sessions
        """
        ALTER TABLE upload_sessions 
        ADD COLUMN IF NOT EXISTS categorization_total INTEGER DEFAULT 0;
        """,
        
        """
        ALTER TABLE upload_sessions 
        ADD COLUMN IF NOT EXISTS categorization_completed INTEGER DEFAULT 0;
        """,
        
        """
        ALTER TABLE upload_sessions 
        ADD COLUMN IF NOT EXISTS categorization_failed INTEGER DEFAULT 0;
        """,
        
        """
        ALTER TABLE upload_sessions 
        ADD COLUMN IF NOT EXISTS categorization_started_at TIMESTAMP WITH TIME ZONE;
        """,
        
        """
        ALTER TABLE upload_sessions 
        ADD COLUMN IF NOT EXISTS categorization_completed_at TIMESTAMP WITH TIME ZONE;
        """,
        
        # Step 4: Create indexes for efficient querying
        """
        CREATE INDEX IF NOT EXISTS idx_files_categorization_status 
        ON uploaded_files(categorization_status, session_id);
        """,
        
        """
        CREATE INDEX IF NOT EXISTS idx_sessions_categorization_status 
        ON upload_sessions(categorization_completed, categorization_total);
        """
    ]
    
    try:
        with engine.connect() as conn:
            # Execute migration steps one by one
            for i, step in enumerate(migration_steps, 1):
                print(f"Executing step {i}/{len(migration_steps)}...")
                conn.execute(text(step))
                conn.commit()
            
        logger.info("Successfully added categorization status tracking fields")
        print("✅ Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}", exc_info=True)
        print(f"❌ Migration failed: {str(e)}")
        sys.exit(1)
    
    finally:
        engine.dispose()

if __name__ == "__main__":
    print("Running migration to add categorization status tracking fields...")
    run_migration()
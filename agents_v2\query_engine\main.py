import os
import json
import faiss
import requests
import numpy as np
import time
import hashlib
import asyncio
import aiofiles
import base64
import uuid  
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from nltk.tokenize import word_tokenize
from rank_bm25 import B<PERSON><PERSON><PERSON><PERSON><PERSON>
from openai import OpenAI
from dotenv import load_dotenv
from typing import List, Dict, Tuple, Any

#  Load environment variables
load_dotenv()

# Configuration 
JINA_API_URL = os.getenv("JINA_API_URL", "https://api.jina.ai/v1/embeddings")
JINA_API_KEY = os.getenv("JINA_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME")

# Initialize the OpenAI client
client = OpenAI(
    base_url=os.getenv("AGENTIC_BASE_URL"),
    api_key=os.getenv("AGENTIC_API_KEY")
)

# Cache directory for parsed PDFs
CACHE_DIR = Path(__file__).parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)


# PDF PARSING & CACHING 

def get_file_hash(file_path: str) -> str:
    """Generate a unique hash for a file to use as a cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_path(pdf_path: str) -> Path:
    """Get the cache file path for a given PDF."""
    file_hash = get_file_hash(pdf_path)
    filename = Path(pdf_path).stem
    return CACHE_DIR / f"{filename}_{file_hash}.txt"

async def get_cached_content(pdf_path: str) -> str:
    """Retrieve cached content for a PDF if it exists."""
    cache_path = get_cache_path(pdf_path)
    if cache_path.exists():
        print(f"✓ Using cached content for {Path(pdf_path).name}")
        async with aiofiles.open(cache_path, 'r', encoding='utf-8') as f:
            return await f.read()
    return None

async def cache_content(pdf_path: str, content: str):
    """Cache the parsed content to a text file."""
    cache_path = get_cache_path(pdf_path)
    async with aiofiles.open(cache_path, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"✓ Cached content for {Path(pdf_path).name}")

def parse_pdf_with_jina(pdf_path: str, max_retries: int = 3) -> str:
    """
    Parse an entire PDF using the JINA AI Reader API with robust retry logic.
    This function is synchronous and designed to be run in a thread pool.
    """
    print(f"📄 Parsing {Path(pdf_path).name} with JINA...")

    try:
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
    except Exception as e:
        raise ValueError(f"Failed to read PDF file: {str(e)}")

    file_size_mb = len(pdf_bytes) / (1024 * 1024)
    if file_size_mb > 50:  # JINA's recommended limit
        print(f"⚠️  Large file detected ({file_size_mb:.1f}MB) - parsing may take longer.")

    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')

    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'HybridRAGLattice/1.0'
    }
    if JINA_API_KEY:
        headers["Authorization"] = f"Bearer {JINA_API_KEY}"

    payload = {
        "url": None,
        "pdf": pdf_base64,
        "filename": Path(pdf_path).name
    }

    for attempt in range(max_retries):
        try:
            timeout = min(300, max(60, int(file_size_mb * 10)))
            print(f"🔄 Attempt {attempt + 1}/{max_retries} (timeout: {timeout}s)")

            response = requests.post("https://r.jina.ai/", json=payload, headers=headers, timeout=timeout)

            if response.status_code == 200:
                content = response.text.strip()
                if content and len(content) > 50 and "Access denied" not in content and "Cloudflare" not in content:
                    print(f"✓ Successfully parsed {Path(pdf_path).name} ({len(content)} chars)")
                    return content
                else:
                    raise ValueError("Invalid or empty response from JINA API.")

            elif response.status_code in [429, 503, 502, 504]:
                wait_time = (2 ** attempt) + np.random.uniform(0, 1)
                print(f"⚠️  JINA API Error ({response.status_code}). Retrying in {wait_time:.2f}s...")
                if attempt < max_retries - 1:
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API persistently failed with status {response.status_code}: {response.text}")
            else:
                raise ValueError(f"JINA API error: {response.status_code} - {response.text}")

        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Connection error: {e}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Network error after {max_retries} attempts: {e}")

    raise ValueError(f"Failed to parse PDF with JINA after {max_retries} attempts.")

async def get_or_parse_pdf_content(pdf_path: str) -> str:
    """
    Orchestrator to get PDF content, preferring cache but parsing if needed.
    """
    cached_content = await get_cached_content(pdf_path)
    if cached_content:
        return cached_content

    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        content = await loop.run_in_executor(executor, parse_pdf_with_jina, pdf_path)

    await cache_content(pdf_path, content)
    return content


# LATTICE HEADER EXTRACTION

async def extract_headers_from_document(pdf_path: str, content: str) -> Dict[str, Any]:
    """Extract potential headers from a document's content using an LLM."""
    filename = Path(pdf_path).name
    print(f"🤖 Extracting headers from {filename}...")

    prompt = f"""
You are an expert document analyst. Your task is to analyze the provided document content and extract potential HEADERS that could be used as column names in an Excel file.



Instructions:
1. **Document Type Analysis**: First, identify the type of document (financial report, contract, resume, research paper, legal document, etc.) and extract headers relevant to that specific document type.

2. **Content-Based Extraction**: Look for:
   - Actual data fields, metrics, or values mentioned in the document
   - Section headers, table columns, or structured information
   - Key entities, dates, numbers, or categorical information
   - Any information that could be systematically extracted and compared across documents

3. **Headers should be**:
   - Specific enough to be meaningful and actionable
   - General enough to be reusable across similar documents
   - Professional and clear
   - Suitable as Excel column headers
   - Based on actual content present in the document

4. **Consider different document types and their relevant headers**:
   - **Financial Documents**: Revenue, Net Income, EBITDA, Assets, Liabilities, Cash Flow, Fiscal Year, Quarter, etc.
   - **Legal Documents**: Contract Value, Effective Date, Parties, Terms, Jurisdiction, Case Number, Filing Date, etc.
   - **Resumes/CVs**: Name, Experience Years, Skills, Education, Previous Companies, Certifications, etc.
   - **Research Papers**: Title, Authors, Abstract, Methodology, Results, Conclusions, Publication Date, etc.
   - **Reports**: Report Type, Author, Key Findings, Recommendations, Executive Summary, etc.
   - **Technical Documents**: Specifications, Requirements, Standards, Compliance Status, etc.
   - **Marketing Materials**: Campaign Name, Target Audience, Budget, ROI, Conversion Rate, etc.
   - **Operational Documents**: Process Name, Department, Timeline, Resources, Outcomes, etc.

5. **Extraction Guidelines**:
   - Focus on structured, extractable data rather than narrative text
   - Look for patterns, lists, tables, or repeated information structures
   - Identify both explicit headers and implicit data categories
   - Consider temporal, numerical, categorical, and entity-based information
   - Extract headers that represent measurable or comparable data points

6. **Quality Control**:
   - Avoid overly generic headers like "Information" or "Data"
   - Ensure headers are specific to the document's content and purpose
   - Prefer headers that would have concrete values (dates, numbers, names, categories)
   - Limit to 10-20 most relevant headers per document

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "headers": ["Header 1", "Header 2", "Header 3", ...]
}}

No other text, explanations, or formatting. Just the JSON.
"""
    try:
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a professional document analyst. Always respond with valid JSON only."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        response_text = response.choices[0].message.content.strip()
        result = json.loads(response_text)

        if "headers" in result and isinstance(result["headers"], list):
            print(f"✓ Extracted {len(result['headers'])} headers from {filename}")
            return {"filename": filename, "pdf_path": pdf_path, "headers": result["headers"], "success": True, "error": None}
        else:
            raise ValueError("Invalid JSON structure in LLM response.")

    except Exception as e:
        print(f"✗ Error extracting headers from {filename}: {e}")
        return {"filename": filename, "pdf_path": pdf_path, "headers": [], "success": False, "error": str(e)}

async def reduce_headers_to_final_set(individual_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Consolidate headers from multiple documents into a final, unified set using an LLM."""
    print("🧠 Reducing all generated headers to a final consolidated set...")

    all_headers = [header for result in individual_results if result["success"] for header in result["headers"]]
    if not all_headers:
        return {"final_headers": [], "success": False, "error": "No headers were extracted to reduce."}

    doc_summary = "\n".join(
        f"Document: {res['filename']}\nHeaders: {', '.join(res['headers'])}\n"
        for res in individual_results if res["success"]
    )

    reduction_prompt = f"""
You are an expert data analyst. Your task is to create a FINAL, CONSOLIDATED set of lattice headers from the lists provided from multiple documents.

CONTEXT:
- Headers have been extracted from several documents.
- The final set should be balanced: comprehensive enough for key data but general enough to apply across the document collection.
- Goal: Create the best possible set of column headers for a data extraction table.

DOCUMENT HEADERS ANALYSIS:
{doc_summary}

ALL COLLECTED HEADERS ({len(all_headers)} total):
{', '.join(sorted(set(all_headers)))}

CONSOLIDATION STRATEGY:
1.  **Group & Merge**: Combine semantically similar headers (e.g., "Total Revenue" and "Revenue" -> "Total Revenue").
2.  **Prioritize**: Favor headers that are frequent or represent critical, extractable data (metrics, dates, names).
3.  **Generalize**: Prefer broader, more applicable terms over overly specific ones (e.g., "Effective Date" over "Agreement Signing Date of 2023").
4.  **Balance**: Include universal headers (e.g., Document Type, Company Name) and domain-specific ones (e.g., EBITDA, Contract Value).
5.  **Final Selection**: Curate a final list of the most valuable and robust headers.

CRITICAL: Respond with ONLY a valid JSON object in this exact format:
{{
    "final_headers": ["Final Header 1", "Final Header 2", ...],
    "reasoning": "A brief explanation of your consolidation approach and key decisions."
}}
"""
    try:
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a data analyst specializing in header consolidation. Respond with valid JSON only."},
                {"role": "user", "content": reduction_prompt}
            ],
            temperature=0.2,
            max_tokens=2000
        )
        response_text = response.choices[0].message.content.strip()
        result = json.loads(response_text)

        if "final_headers" in result and isinstance(result["final_headers"], list):
            print(f"✅ Successfully reduced to {len(result['final_headers'])} final headers.")
            print(f"💡 LLM Reasoning: {result.get('reasoning', 'N/A')}")
            return {"final_headers": result["final_headers"], "success": True, "error": None}
        else:
            raise ValueError("Invalid JSON structure in LLM reduction response.")

    except Exception as e:
        print(f"✗ Error in header reduction: {e}")
        return {"final_headers": [], "success": False, "error": str(e)}

async def extract_and_consolidate_lattice_headers(pdf_paths: List[str]) -> List[str]:
    """
    Main workflow for Phase 1: Parse PDFs, extract headers from each, and consolidate them.
    """
    print(f"\n{'='*60}\nPHASE 1: DYNAMIC LATTICE HEADER EXTRACTION\n{'='*60}")

    # Step 1: Parse all documents in parallel
    print(f"📄 Parsing {len(pdf_paths)} documents...")
    parsed_contents = {}
    parse_tasks = [get_or_parse_pdf_content(pdf_path) for pdf_path in pdf_paths]
    results = await asyncio.gather(*parse_tasks, return_exceptions=True)
    for i, res in enumerate(results):
        if isinstance(res, Exception):
            print(f"❌ Failed to parse {Path(pdf_paths[i]).name}: {res}")
        else:
            parsed_contents[pdf_paths[i]] = res
    
    if not parsed_contents:
        print("❌ No documents could be parsed. Aborting.")
        return []

    # Step 2: Extract headers from each parsed document in parallel
    print(f"🤖 Extracting headers from {len(parsed_contents)} successfully parsed documents...")
    extract_tasks = [extract_headers_from_document(path, content) for path, content in parsed_contents.items()]
    individual_results = await asyncio.gather(*extract_tasks, return_exceptions=True)

    # Filter out exceptions from header extraction
    successful_extractions = [res for res in individual_results if not isinstance(res, Exception) and res["success"]]

    # Step 3: Reduce headers to a final consolidated set
    if not successful_extractions:
        print("⚠️ No headers were successfully extracted. Cannot generate a final set.")
        return []

    final_result = await reduce_headers_to_final_set(successful_extractions)

    if final_result["success"]:
        print(f"🎯 Phase 1 Complete. Generated {len(final_result['final_headers'])} consolidated headers.")
        return final_result["final_headers"]
    else:
        print(f"❌ Phase 1 Failed: {final_result['error']}")
        return []


# HYBRID RAG PROCESSING & QUERYING

def calculate_tokens(text: str) -> int:
    """Estimate token count (4 chars ≈ 1 token)."""
    return len(text) // 4

def chunk_text(text: str, max_tokens: int = 7000) -> List[Dict[str, str]]:
    """Chunk text into segments, assigning a unique ID to each chunk."""
    print(f"Chunking text (~{calculate_tokens(text)} tokens) into max {max_tokens}-token chunks with unique IDs.")
    max_chars = max_tokens * 4
    sentences = text.split('. ')
    chunks, current_chunk_text = [], ""

    for sentence in sentences:
        sentence_with_period = sentence + '. '
        if len(current_chunk_text) + len(sentence_with_period) > max_chars:
            if current_chunk_text:
                chunks.append({
                    "chunk_id": f"chunk-{uuid.uuid4()}",
                    "text": current_chunk_text.strip()
                })
            current_chunk_text = sentence_with_period
        else:
            current_chunk_text += sentence_with_period

    if current_chunk_text.strip():
        chunks.append({
            "chunk_id": f"chunk-{uuid.uuid4()}",
            "text": current_chunk_text.strip()
        })

    print(f"Created {len(chunks)} chunks.")
    return chunks

def get_jina_embeddings(texts: List[str], batch_size: int = 50, max_retries: int = 3) -> np.ndarray:
    """Fetch embeddings from Jina API with batching and retry logic."""
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {JINA_API_KEY}"}
    all_embeddings = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        for attempt in range(max_retries):
            try:
                data = {"model": "jina-embeddings-v2-base-en", "input": batch_texts}
                response = requests.post(JINA_API_URL, headers=headers, json=data, timeout=60)
                response.raise_for_status()
                batch_embeddings = response.json()["data"]
                all_embeddings.extend([e["embedding"] for e in batch_embeddings])
                break
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise
    
    return np.array(all_embeddings).astype("float32")

def build_dense_index(chunks: List[Dict[str, str]]) -> faiss.IndexIDMap:
    """Build a FAISS index from document chunks using Jina embeddings."""
    print("Building dense (FAISS) index...")
    chunk_texts = [chunk['text'] for chunk in chunks]
    embeddings = get_jina_embeddings(chunk_texts)
    dim = embeddings.shape[1]
    index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
    index.add_with_ids(embeddings, np.arange(len(chunks)))
    print(f"✓ Dense index built with {index.ntotal} vectors.")
    return index

def build_sparse_index(chunks: List[Dict[str, str]]) -> BM25Okapi:
    """Build a BM25 sparse index from document chunks."""
    print("Building sparse (BM25) index...")
    chunk_texts = [chunk['text'] for chunk in chunks]
    tokenized_chunks = [word_tokenize(chunk.lower()) for chunk in chunk_texts]
    bm25 = BM25Okapi(tokenized_chunks)
    print(f"✓ Sparse index built for {len(tokenized_chunks)} documents.")
    return bm25

class DocumentProcessor:
    """A class to hold the processed state of a single document."""
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.document_info = {"file_name": Path(pdf_path).name, "file_path": pdf_path}
        self.content = None
        self.chunks: List[Dict[str, str]] = [] 
        self.faiss_index = None
        self.bm25_index = None

    async def process(self):
        """Full processing pipeline for a single document."""
        print(f"\n🔄 Processing document: {self.document_info['file_name']}")
        self.content = await get_or_parse_pdf_content(self.pdf_path)
        self.chunks = chunk_text(self.content)
        if self.chunks:
            self.faiss_index = build_dense_index(self.chunks)
            self.bm25_index = build_sparse_index(self.chunks)
            print(f"✅ Document processed: {self.document_info['file_name']}")
        else:
            print(f"⚠️ No chunks created for {self.document_info['file_name']}. Skipping indexing.")
        return self

def hybrid_query(query: str, processor: DocumentProcessor, top_k: int = 5, rrf: bool = True) -> List[Dict[str, str]]:
    """Perform a hybrid search and return the full retrieved chunk objects."""
    print(f"  Executing hybrid query for: '{query}'")
    if not processor.chunks:
        return []
        
    # Sparse search
    tokenized_query = word_tokenize(query.lower())
    bm25_scores = processor.bm25_index.get_scores(tokenized_query)
    bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]
    
    # Dense search
    query_vec = get_jina_embeddings([query])
    _, faiss_top_ids = processor.faiss_index.search(query_vec, top_k)
    faiss_top_ids = faiss_top_ids[0]

    # Reciprocal Rank Fusion (RRF)
    ranks = {idx: 0 for idx in range(len(processor.chunks))}
    for rank, idx in enumerate(bm25_top_ids):
        ranks[idx] += 1 / (rank + 60)
    for rank, idx in enumerate(faiss_top_ids):
        ranks[idx] += 1 / (rank + 60)
        
    combined_ids = sorted(ranks.keys(), key=lambda x: ranks[x], reverse=True)[:top_k]

    return [processor.chunks[idx] for idx in combined_ids]

def generate_structured_answer(query: str, retrieved_chunks: List[Dict[str, str]], model: str = "agentic-large") -> Dict[str, Any]:
    """
    Generate a structured JSON answer from an LLM, including answer, confidence, and citations.
    """
    print(f"  Generating structured answer for: '{query}'")
    
    # Create a context string
    context = "\n\n".join(
        f"--- Chunk ID: {chunk['chunk_id']} ---\n{chunk['text']}"
        for chunk in retrieved_chunks
    )

    prompt = f"""
You are an expert Question-Answering assistant. Your task is to analyze the provided context and answer the user's question. You must follow all instructions carefully.

CONTEXT:
{context}

QUESTION: {query}

INSTRUCTIONS:
1.  **Answer the Question**: Formulate a concise and accurate answer based *only* on the information in the provided context.
2.  **Assess Confidence**: Provide a confidence score between 0.0 and 1.0, representing how certain you are that your answer is correct and complete based on the context.
3.  **Provide Citations**: Identify the `Chunk ID` of every chunk you used to formulate your answer. The citations must be a list of strings.
4.  **Handle Missing Information**: If the answer cannot be found in the context, state that clearly in the "answer" field and set the confidence score to a low value (e.g., 0.1).

CRITICAL: You must respond with ONLY a single, valid JSON object in the following format. Do not add any text before or after the JSON.

{{
  "answer": "Your concise answer here.",
  "confidence_score": 0.95,
  "citations": ["chunk-uuid-of-source-1", "chunk-uuid-of-source-2"]
}}
"""
    
    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are an AI assistant that provides structured answers in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )
        response_text = response.choices[0].message.content.strip()
        return json.loads(response_text)
    except Exception as e:
        print(f"  ✗ Error generating structured answer: {e}")
        # Return a structured error message
        return {
            "answer": f"An error occurred while generating the answer: {e}",
            "confidence_score": 0.0,
            "citations": []
        }

async def run_parallel_lattice_analysis(
    pdf_paths: List[str],
    lattice_headers: List[str],
    top_k: int = 5,
    rrf: bool = True
) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Main pipeline for Phase 2: Process documents and query them in parallel with lattice headers.
    """
    print(f"\n{'='*60}\nPHASE 2: PARALLEL LATTICE ANALYSIS\n{'='*60}")
    
    # Step 1: Process all documents in parallel
    doc_processors = [DocumentProcessor(path) for path in pdf_paths]
    process_tasks = [proc.process() for proc in doc_processors]
    processed_docs = await asyncio.gather(*process_tasks, return_exceptions=True)
    
    successful_processors = [p for p in processed_docs if isinstance(p, DocumentProcessor) and p.chunks]
    if not successful_processors:
        print("❌ No documents were successfully processed for RAG analysis. Aborting.")
        return {}

    # Step 2: Query all documents with all headers in parallel
    print(f"\n🔍 Querying {len(successful_processors)} documents with {len(lattice_headers)} headers...")
    final_results = {}

    async def query_and_generate(processor: DocumentProcessor, header: str):
        # Run the blocking operations in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        retrieved_chunks = await loop.run_in_executor(None, hybrid_query, header, processor, top_k, rrf)
        answer = await loop.run_in_executor(None, generate_answer, header, retrieved_chunks)
        return processor.document_info['file_name'], header, answer

    all_tasks = [
        query_and_generate(processor, header)
        for processor in successful_processors
        for header in lattice_headers
    ]
    
    all_results = await asyncio.gather(*all_tasks, return_exceptions=True)
    
    # Organize results by document
    for res in all_results:
        if isinstance(res, Exception):
            continue
        doc_name, header, answer_obj = res
        if doc_name not in final_results:
            final_results[doc_name] = {}
        final_results[doc_name][header] = answer_obj
            
    print(f"\n✅ Phase 2 Complete. Analysis finished for {len(successful_processors)} documents.")
    return final_results


# RESULTS PROCESSING & MAIN EXECUTION 

def save_results_to_file(results: Dict[str, Dict[str, Dict[str, Any]]], output_path: str = "lattice_results.txt"):
    """Save the final analysis to a formatted text file, including confidence and citations."""
    result_string = "LATTICE ANALYSIS RESULTS\n" + "=" * 60 + "\n\n"
    for doc_name, headers_data in results.items():
        result_string += f"📄 DOCUMENT: {doc_name}\n" + "-" * 40 + "\n"
        for header, data in headers_data.items():
            answer = data.get('answer', 'N/A')
            confidence = data.get('confidence_score', 0.0)
            citations = data.get('citations', [])
            
            result_string += f"🎯 {header}:\n"
            result_string += f"   - Answer: {answer}\n"
            result_string += f"   - Confidence: {confidence:.2%}\n"
            result_string += f"   - Citations: {', '.join(citations) if citations else 'None'}\n\n"
        result_string += "\n"

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(result_string)
    print(f"\n📊 Results saved to: {output_path}")

async def main():
    """Main execution entry point for the entire pipeline."""
    print(" STARTING HYBRID RAG & LATTICE ANALYSIS PIPELINE 🚀")
    
    pdf_paths = [
        "../../mp_materials/pdfs/form-10-k.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf",
        "../../mp_materials/pdfs/employment-agreement.pdf",
        "../../mp_materials/pdfs/form-10-q-2.pdf"
    ]
    
    valid_paths = [p for p in pdf_paths if Path(p).exists()]
    if len(valid_paths) != len(pdf_paths):
        print("⚠️  Warning: Some PDF paths were not found and will be skipped.")
    
    if not valid_paths:
        print("❌ Error: No valid PDF files found. Exiting.")
        return

    try:
        lattice_headers = await extract_and_consolidate_lattice_headers(valid_paths)
        if not lattice_headers:
            print("❌ Pipeline halted because no lattice headers could be generated.")
            return

        print("\n FINAL CONSOLIDATED LATTICE HEADERS TO BE USED FOR ANALYSIS:")
        for i, header in enumerate(lattice_headers, 1):
            print(f"  {i:2d}. {header}")

        results = await run_parallel_lattice_analysis(
            pdf_paths=valid_paths,
            lattice_headers=lattice_headers,
            top_k=5,
            rrf=True
        )

        if results:
            save_results_to_file(results)
            print("\n Analysis completed successfully!")
        else:
            print("\n Analysis finished, but no results were generated.")

    except Exception as e:
        print(f"\n❌ An unexpected error occurred in the main pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

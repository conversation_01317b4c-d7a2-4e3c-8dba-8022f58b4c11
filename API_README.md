# FastAPI Upload API with PostgreSQL

## Overview

This FastAPI application handles bulk document uploads with job polling, now integrated with PostgreSQL for production-ready data storage.

## PostgreSQL Integration

### Database Schema

The application uses PostgreSQL with the following enhanced schema:

#### Tables

1. **upload_sessions**
   - Tracks upload sessions with user and company information
   - Stores metadata as JSONB for flexible data
   - Includes total file count and processing statistics

2. **uploaded_files**
   - Individual file records with S3 bucket information
   - File hash for deduplication
   - Processing status tracking
   - Metadata storage for extracted information

3. **file_processing_results**
   - Stores all processing results
   - Extracted text and AI summaries
   - Entity extraction (companies, people, dates, amounts)
   - Document classifications
   - Qdrant vector IDs for similarity search

### Key PostgreSQL Features Used

- **JSONB columns** for flexible metadata storage
- **UUID primary keys** for better scalability
- **Composite indexes** for query performance
- **Connection pooling** for handling concurrent requests
- **Async support** with asyncpg for better performance

## API Endpoints

### 1. POST /api/v1/upload/prepare
Prepare for uploads with optional metadata:
```json
POST /api/v1/upload/prepare?user_id=user123&company_name=Acme&session_type=financial
{
  "filenames": ["report.pdf", "summary.xlsx"]
}
```

### 2. POST /api/v1/upload/complete
Confirm uploads with file details:
```json
{
  "session_id": "uuid",
  "files": [
    {
      "filename": "report.pdf",
      "bucket_id": "uploads/uuid/report.pdf",
      "file_size": 1024000,
      "content_type": "application/pdf"
    }
  ]
}
```

### 3. POST /api/v1/process/{session_id}
Start processing files (runs in background)

### 4. GET /api/v1/status/{session_id}
Get current processing status

### 5. GET /api/v1/results/{session_id}
Get detailed processing results including:
- Extracted text summaries
- Entity counts
- Document classifications
- Processing metadata

## Data Storage Strategy

### What Gets Stored

1. **Session Level**
   - User identification
   - Company association
   - Session type (financial, legal, general)
   - Processing statistics
   - Timestamps

2. **File Level**
   - Original filename and S3 location
   - File hash for deduplication
   - Size and content type
   - Processing status
   - Extracted metadata

3. **Processing Results**
   - Extracted text content
   - AI-generated summaries
   - Named entities (companies, people, dates, amounts)
   - Document classifications
   - Embeddings/vector IDs
   - Processing metrics

### Database Operations

```python
# Example: Query sessions by company
sessions = db.query(UploadSession).filter(
    UploadSession.company_name == "Acme Corp"
).order_by(UploadSession.created_at.desc()).all()

# Example: Find duplicate files
duplicates = db.query(UploadedFile).filter(
    UploadedFile.file_hash == calculated_hash
).all()

# Example: Get processing results with entities
results = db.query(FileProcessingResult).filter(
    FileProcessingResult.entities['companies'].astext.contains('Apple')
).all()
```

## Running with PostgreSQL

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Run database migrations:**
```bash
alembic upgrade head
```

3. **Start the API:**
```bash
python run_api.py
```

## Environment Variables

```env
# PostgreSQL Configuration
DATABASE_URL=postgresql://user:pass@host:port/dbname
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40
DATABASE_POOL_TIMEOUT=30

# DigitalOcean Spaces
DO_SPACES_ENDPOINT=https://your-space.digitaloceanspaces.com/
DO_SPACES_ACCESS_KEY=your_access_key
DO_SPACES_SECRET_KEY=your_secret_key
DO_SPACES_BUCKET=your_bucket_name
DO_SPACES_REGION=region
```

## Performance Considerations

1. **Connection Pooling**: Configured with 20 connections + 40 overflow
2. **Indexes**: Created on frequently queried columns (user_id, company_name, status)
3. **JSONB Queries**: Use PostgreSQL's native JSON operators for efficient queries
4. **Async Support**: Use asyncpg for concurrent database operations

## Migration from SQLite

The code automatically detects the database type and uses appropriate column types:
- SQLite: String columns for UUIDs, Text for JSON
- PostgreSQL: Native UUID and JSONB types

## Security Notes

- Database credentials should be stored securely
- Use SSL connections for production PostgreSQL
- Implement row-level security for multi-tenant applications
- Regular backups recommended
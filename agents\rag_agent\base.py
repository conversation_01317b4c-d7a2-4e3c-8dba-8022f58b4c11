import logging
import os
import requests
from openai import OpenAI
from typing import List, Dict
from qdrant_client import QdrantClient
from qdrant_client import models
from qdrant_client.models import Filter, FieldCondition, MatchValue

from .document_ingestion import DocumentIngestion

logger = logging.getLogger(__name__)

# System prompt for OpenAI
GPT_SYSTEM_PROMPT =  (
    "You are an AI assistant tasked with answering questions using the provided context. "
    "Your role is to provide accurate, context-based responses while being transparent about your confidence level.\n\n"
    "Response Guidelines:\n"
    "1. If the answer is present in the context:\n"
    "   - Provide a concise and accurate response\n"
    "   - Include confidence_score between 0-1 (1 being highest confidence) based on how well the context supports your answer\n"
    "   - List all relevant context indices and document IDs used\n\n"
    "2. If the information is insufficient:\n"
    "   - Respond with: 'I cannot answer with the provided context'\n"
    "   - Include confidence_score between 0-1 (1 meaning you are completely certain the answer cannot be found in the context)\n"
    "   - Set context indices and doc_ids as empty arrays\n\n"
    "RESPONSE_FORMAT_GUIDELINES: "
    "Your response must be in JSON format with the following structure:\n\n"
    "{\n"
    '  "answer": "Your generated answer",\n'
    '  "confidence_score": float between 0-1,\n'
    '  "used_context_indices": [indices of context snippets used],\n'
    '  "used_doc_ids": [document IDs of used snippets]\n'
    "}\n\n"
    "CRITICAL JSON REQUIREMENTS:\n"
    "- The 'answer' field must be a STRING, not an object or array\n"
    "- Use standard JSON number format (no underscores in numbers)\n"
    "- Ensure all strings are properly quoted\n"
    "- Validate JSON syntax before responding\n\n"
    "Remember to:\n"
    "- Think deeply about each context snippet you use\n"
    "- Verify all context indices and doc_ids\n"
    "- Provide accurate confidence scores based on context quality and completeness\n"
    "- Never use knowledge outside the provided context\n"
    "- Never mention the Chunk indices used or Confidence score in the generated answer.\n"
    "- Double-check your JSON formatting"
)


def search_graph(query: str, endpoint_url: str = "http://localhost:8000/api/search") -> Dict:
    """
    Search the graph database by making a request to the specified endpoint.
    """
    try:
        payload = {
            "query": query
        }
        
        logger.info(f"Making graph search request to {endpoint_url} with query: {query}")
        
        response = requests.post(
            endpoint_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30 
        )
        
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"Graph search completed successfully, received {len(result)} results" if isinstance(result, (list, dict)) else "Graph search completed successfully")
        
        return result
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error making request to graph search endpoint: {str(e)}")
        return {"error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in graph search: {str(e)}")
        return {"error": str(e)}


def search_qdrant(
    query: str, 
    user_id: str, 
    collection_name: str,
    qdrant_url: str = "http://localhost:6333",
    k: int = 5
) -> List[Dict]:
    """
    RAG agent that performs hybrid retrieval using both dense and sparse embeddings.
    
    Args:
        query: The search query
        user_id: User ID to filter results by
        collection_name: Name of the Qdrant collection
        qdrant_url: Qdrant server URL
        k: Number of results to return
        
    Returns:
        List of retrieved chunks with text, doc_uuid, and score
    """
    try:
        # Initialize document ingestion and Qdrant client
        doc_ingestion = DocumentIngestion()
        qdrant_client = QdrantClient(url=qdrant_url)
        
        # Initialize embedding models
        doc_ingestion.init_dense_embedding_model()
        doc_ingestion.init_sparse_embedding_model()
        
        # Generate embeddings for the query
        logger.info(f"Generating embeddings for query: {query}")
        
        # Get dense embedding
        dense_embedding = doc_ingestion.get_dense_embeddings(query)
        if dense_embedding is None:
            logger.error("Failed to generate dense embedding")
            return []
        
        # Get sparse embedding
        sparse_embedding_obj = doc_ingestion.get_sparse_embeddings(query)
        if sparse_embedding_obj is None:
            logger.error("Failed to generate sparse embedding")
            return []
        
        # Convert sparse embedding to dict format expected by Qdrant
        sparse_embedding = {
            'indices': sparse_embedding_obj.indices,
            'values': sparse_embedding_obj.values
        }
        
        # Perform hybrid retrieval
        logger.info(f"Performing hybrid retrieval for user_id: {user_id}")
        
        # Set up prefetch queries for both embedding types
        prefetch = [
            models.Prefetch(
                query=dense_embedding,
                using="dense",
                limit=k,
            ),
            models.Prefetch(
                query=models.SparseVector(**sparse_embedding),
                using="sparse",
                limit=k,
            ),
        ]

        # Query with RRF fusion and filter by doc_id
        response = qdrant_client.query_points(
            collection_name=collection_name,
            prefetch=prefetch,
            query=models.FusionQuery(
                fusion=models.Fusion.RRF,
            ),
            query_filter=Filter(
                must=[
                    FieldCondition(key="user_id", match=MatchValue(value=user_id)),
                ]
            ),
            with_payload=True,
            limit=k,
        )

        # Process and return results
        results = [
            {
                "text": point.payload["text"],
                "doc_id": point.payload["doc_id"],
                "score": point.score,  # Including score from fusion
            }
            for point in response.points
        ]
        
        logger.info(f"Retrieved {len(results)} chunks for query")
        return results
        
    except Exception as e:
        logger.error(f"Error in RAG agent: {str(e)}")
        return []
    

def generate_answer(query: str, search_results: List[Dict], openai_api_key: str = os.getenv("OPENAI_API_KEY")) -> str:
    """
    Generate an answer using OpenAI based on search results from search_qdrant.
    
    Args:
        query: The user's question
        search_results: List of search results from search_qdrant function
        openai_api_key: OpenAI API key
        
    Returns:
        Generated answer from OpenAI
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=openai_api_key)
        
        # Format the search results into context
        context = _process_search_results(search_results)
        
        # Generate answer using OpenAI
        answer = _call_openai_model(client, query, context)
        
        logger.info(f"Generated answer for query: {query}")
        return answer
        
    except Exception as e:
        logger.error(f"Error generating answer: {str(e)}")
        return f"Sorry, I encountered an error while generating the answer: {str(e)}"


def _process_search_results(search_results: List[Dict]) -> str:
    """
    Process search results into a formatted context string.
    
    Args:
        search_results: List of search results from search_qdrant
        
    Returns:
        Formatted context string
    """
    if not search_results:
        return "No relevant context found."
    
    context = ""
    for idx, result in enumerate(search_results):
        text = result.get("text", "")
        doc_id = result.get("doc_id", "unknown")
        score = result.get("score", 0.0)
        
        context += f"Chunk {idx + 1} (Relevance Score: {score:.3f}):\n"
        context += f"Content: {text}\n"
        context += f"Document ID: {doc_id}\n"
        context += "-" * 50 + "\n"
    
    return context


def _call_openai_model(client: OpenAI, query: str, context: str) -> str:
    """
    Call the OpenAI GPT model to generate an answer.
    
    Args:
        client: OpenAI client instance
        query: User's question
        context: Formatted context from search results
        
    Returns:
        Generated answer
    """
    user_prompt = f"Question: {query}\n\nContext:\n{context}"
    
    messages = [
        {"role": "system", "content": GPT_SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt},
    ]
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Using a more accessible model
            temperature=0.1,
            messages=messages,
            max_tokens=1000
        )
        
        generated_answer = response.choices[0].message.content.strip()
        logger.info(f"OpenAI response generated successfully (length: {len(generated_answer)} chars)")
        
        return generated_answer
        
    except Exception as e:
        logger.error(f"Error calling OpenAI API: {str(e)}")
        raise e

if __name__ == "__main__":
    # Example usage of search_qdrant
    qdrant_results = search_qdrant(query="What is the capital of France?", user_id="default_user", collection_name="documents")
    print("Qdrant results:", qdrant_results)

"""
Check the current state of a session in the database
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from api.config import settings

def check_session_state(session_id: str):
    """Check the state of a specific session"""
    
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            # Check session state
            session_sql = """
            SELECT 
                id::text as id,
                categorization_started_at,
                categorization_completed_at,
                categorization_total,
                categorization_completed,
                categorization_failed
            FROM upload_sessions
            WHERE id::text = :session_id;
            """
            
            result = conn.execute(text(session_sql), {"session_id": session_id})
            session_data = result.fetchone()
            
            if session_data:
                print(f"\nSession State for {session_id}:")
                print(f"  Started at: {session_data[1]}")
                print(f"  Completed at: {session_data[2]}")
                print(f"  Total files: {session_data[3]}")
                print(f"  Completed: {session_data[4]}")
                print(f"  Failed: {session_data[5]}")
            else:
                print(f"Session {session_id} not found")
            
            # Check file status counts
            files_sql = """
            SELECT 
                categorization_status,
                COUNT(*) as count
            FROM uploaded_files
            WHERE session_id::text = :session_id
            GROUP BY categorization_status;
            """
            
            result = conn.execute(text(files_sql), {"session_id": session_id})
            file_counts = result.fetchall()
            
            print(f"\nFile Status Counts:")
            for status, count in file_counts:
                print(f"  {status}: {count}")
                
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        engine.dispose()

if __name__ == "__main__":
    session_id = "188c1893-ebdf-2f5b-b9d4-60208c451927"
    check_session_state(session_id)